import sys
import os
import subprocess
import json
import base64
import requests
import shutil
import time 
import hashlib
import psutil
import threading
import queue
from pathlib import Path
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QFileDialog,
    QLabel, QMessageBox, QMainWindow, QProgressBar, QGroupBox, QSpinBox,
    QCheckBox, QFrame, QDialog, QGridLayout, QSizePolicy, QListWidget,
    QListWidgetItem, QHeaderView, QTableWidget, QTableWidgetItem, QComboBox,
    QMenu, QAction, QLineEdit, QRadioButton, QButtonGroup, QScrollArea
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, QRect, QObject
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor, QBrush
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import login

# 软件配置
SOFTWARE_NAME = "疾风3.6"
SOFTWARE_VERSION = " | 本软件仅供内部学习交流，禁止非法用途。   "

# 导入聊天室模块
from chatroom import ChatRoom

# 导入视频API模块
from video_api import VideoAPI

# 导入风火轮二代处理器
from Codebase_Fenghuo_v2 import FenghuoV2Processor
from Codebase_Fenghuo_v2_nvenc import FenghuoV2NvencProcessor
# 导入鸭嘴兽处理器
from Codebase_Platypus1 import PlatypusProcessor
# 导入夏日狂欢处理器
from Codebase_KuangSha import KuangShaProcessor
# 导入金刚狼处理器
from Codebase_Wolverine import WolverineProcessor


class DragDropTableWidget(QTableWidget):
    """支持拖拽排序的表格控件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDragDropMode(QTableWidget.InternalMove)
        self.setDefaultDropAction(Qt.MoveAction)
        self.setSelectionBehavior(QTableWidget.SelectRows)

    def dropEvent(self, event):
        """处理拖拽放置事件"""
        if event.source() == self and event.dropAction() == Qt.MoveAction:
            # 获取拖拽的源行和目标行
            source_row = self.currentRow()
            drop_row = self.indexAt(event.pos()).row()

            if drop_row == -1:
                drop_row = self.rowCount() - 1

            if source_row != drop_row and source_row >= 0 and drop_row >= 0:
                # 通知父对话框更新数据
                parent_dialog = self.parent()
                while parent_dialog and not isinstance(parent_dialog, VideoListDialog):
                    parent_dialog = parent_dialog.parent()

                if parent_dialog:
                    parent_dialog.move_video(source_row, drop_row)

        super().dropEvent(event)




class VideoListDialog(QDialog):
    """视频列表详情对话框"""

    def __init__(self, parent=None, video_list=None, title="视频列表"):
        super().__init__(parent)
        self.video_list = video_list or []
        self.dialog_title = title
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle(self.dialog_title)
        self.setFixedSize(600, 400)
        self.setModal(True)

        # 设置窗口标志，防止移动时UI错乱
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel(f"{self.dialog_title} ({len(self.video_list)} 个视频)")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("list_title")
        layout.addWidget(title_label)

        # 视频列表表格（支持拖拽排序）
        self.table = DragDropTableWidget()
        self.table.setColumnCount(2)
        self.table.setHorizontalHeaderLabels(["文件名", "时长"])
        self.table.setObjectName("video_table")

        # 设置右键菜单
        self.table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_context_menu)

        # 设置表格属性
        self.table.setAlternatingRowColors(False)  # 禁用交替行颜色
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.table.setShowGrid(False)  # 隐藏网格线
        self.table.verticalHeader().setVisible(True)  # 显示行号
        self.table.verticalHeader().setDefaultSectionSize(30)  # 设置行高

        # 设置列宽
        header = self.table.horizontalHeader()
        header.setStretchLastSection(False)
        self.table.setColumnWidth(0, 420)  # 文件名列
        self.table.setColumnWidth(1, 120)  # 时长列
        header.setStretchLastSection(True)  # 最后一列自适应

        # 填充数据
        self.populate_table()

        # 添加拖拽提示
        drag_hint = QLabel("💡 提示：可以拖拽视频行来调整处理顺序，右键可以删除/清空")
        drag_hint.setAlignment(Qt.AlignCenter)
        drag_hint.setObjectName("drag_hint")
        layout.addWidget(drag_hint)

        layout.addWidget(self.table)

        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.setObjectName("close_btn")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)

        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background: #1a1a2e;
                color: white;
                font-family: "Microsoft YaHei";
            }

            #list_title {
                font-size: 16px;
                font-weight: bold;
                color: #e94560;
                margin: 10px 0;
            }

            #drag_hint {
                font-size: 12px;
                color: #ffa726;
                margin: 5px 0;
                padding: 5px;
                background: rgba(255, 167, 38, 0.1);
                border-radius: 4px;
                border: 1px solid rgba(255, 167, 38, 0.3);
            }

            #video_table {
                background: #2a2a3e;
                border: 1px solid #533483;
                border-radius: 6px;
                gridline-color: transparent;
                selection-background-color: #6200ea;
                selection-color: white;
                outline: none;
                color: white;
            }

            #video_table::item {
                padding: 10px 8px;
                border: none;
                color: white;
                background: #2a2a3e;
            }

            #video_table::item:selected {
                background: #6200ea !important;
                color: white !important;
            }

            #video_table::item:hover {
                background: rgba(98, 0, 234, 0.3);
                color: white;
            }

            QHeaderView::section {
                background: #533483;
                color: white;
                padding: 12px 8px;
                border: none;
                font-weight: bold;
                font-size: 12px;
            }

            QHeaderView::section:hover {
                background: #6200ea;
            }

            /* 垂直表头（行号）样式 */
            QHeaderView::section:vertical {
                background: #444;
                color: #bbb;
                padding: 4px;
                border: none;
                font-size: 10px;
                min-width: 30px;
            }

            #close_btn {
                background: #6200ea;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                min-width: 100px;
            }

            #close_btn:hover {
                background: #7c4dff;
            }

            #close_btn:pressed {
                background: #3700b3;
            }
        """)

    def populate_table(self):
        """填充表格数据"""
        self.table.setRowCount(len(self.video_list))

        for i, video_info in enumerate(self.video_list):
            # 文件名
            filename = os.path.basename(video_info['path'])
            filename_item = QTableWidgetItem(filename)
            filename_item.setToolTip(video_info['path'])  # 设置完整路径为提示

            # 强制设置颜色，确保所有行都是深色背景
            filename_item.setForeground(QColor(255, 255, 255))  # 白色文字
            filename_item.setBackground(QColor(42, 42, 62))  # 统一深色背景

            self.table.setItem(i, 0, filename_item)

            # 时长
            duration = video_info.get('duration', 0)
            duration_str = self.format_duration(duration)
            duration_item = QTableWidgetItem(duration_str)
            duration_item.setTextAlignment(Qt.AlignCenter)  # 居中对齐

            # 强制设置颜色，确保所有行都是深色背景
            duration_item.setForeground(QColor(255, 255, 255))  # 白色文字
            duration_item.setBackground(QColor(42, 42, 62))  # 统一深色背景

            self.table.setItem(i, 1, duration_item)

    def format_duration(self, seconds):
        """格式化时长显示为00:00:00格式"""
        if seconds <= 0:
            return "00:00:00"

        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)

        return f"{hours:02d}:{minutes:02d}:{secs:02d}"

    def move_video(self, source_row, target_row):
        """移动视频项的位置"""
        if source_row == target_row:
            return

        # 移动video_list中的项
        video_item = self.video_list.pop(source_row)
        self.video_list.insert(target_row, video_item)

        # 重新填充表格以反映新的顺序
        self.populate_table()

        # 选中移动后的行
        self.table.selectRow(target_row)

        # 更新标题中的视频数量（虽然数量没变，但可以刷新显示）
        title_label = self.findChild(QLabel, "list_title")
        if title_label:
            title_label.setText(f"{self.dialog_title} ({len(self.video_list)} 个视频)")

    def get_video_list(self):
        """获取当前排序后的视频列表"""
        return self.video_list.copy()

    def show_context_menu(self, position):
        """显示右键菜单"""
        if self.table.itemAt(position):
            menu = QMenu(self)

            # 删除单个视频
            delete_action = QAction("删除选中视频", self)
            delete_action.triggered.connect(self.delete_selected_video)
            menu.addAction(delete_action)

            menu.addSeparator()

            # 清空列表
            clear_action = QAction("清空列表", self)
            clear_action.triggered.connect(self.clear_video_list)
            menu.addAction(clear_action)

            menu.exec_(self.table.mapToGlobal(position))
        else:
            # 点击空白区域，只显示清空列表选项
            menu = QMenu(self)
            clear_action = QAction("清空列表", self)
            clear_action.triggered.connect(self.clear_video_list)
            menu.addAction(clear_action)
            menu.exec_(self.table.mapToGlobal(position))

    def delete_selected_video(self):
        """删除选中的视频"""
        current_row = self.table.currentRow()
        if current_row >= 0 and current_row < len(self.video_list):
            # 从视频列表中删除
            del self.video_list[current_row]
            # 重新填充表格
            self.populate_table()
            # 更新标题显示视频数量
            self.update_title()

    def clear_video_list(self):
        """清空视频列表"""
        reply = QMessageBox.question(self, "确认清空", "确定要清空所有视频吗？",
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.video_list.clear()
            self.populate_table()
            # 更新标题显示视频数量
            self.update_title()

    def update_title(self):
        """更新标题中的视频数量"""
        title_label = self.findChild(QLabel)
        if title_label and hasattr(title_label, 'objectName') and title_label.objectName() == "list_title":
            title_label.setText(f"{self.dialog_title} ({len(self.video_list)} 个视频)")


class SingleVideoSettingsDialog(QDialog):
    """单视频推流设置对话框"""

    def __init__(self, parent=None, selected_prompt="随机", custom_prompt=""):
        super().__init__(parent)
        self.selected_prompt = selected_prompt
        self.custom_prompt = custom_prompt
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("疾风AI-指定推流设置")
        self.setFixedSize(600, 500)
        self.setModal(True)

        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 30, 30, 30)

        # 标题
        title_label = QLabel("选择推流提示词")
        title_label.setObjectName("settings_title")
        main_layout.addWidget(title_label)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setObjectName("scroll_area")

        # 滚动内容
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setSpacing(15)

        # 创建按钮组
        self.button_group = QButtonGroup()

        # 预设提示词列表（中文显示，英文值）
        prompt_options = [
            ("随机", "random"),
            ("自然风景", "nature"),
            ("森林", "forest"),
            ("海洋", "ocean"),
            ("山脉", "mountain"),
            ("城市", "city"),
            ("日落", "sunset"),
            ("花朵", "flower"),
            ("动物", "animal"),
            ("天空", "sky"),
            ("水", "water"),
            ("火", "fire"),
            ("雪", "snow"),
            ("沙漠", "desert"),
            ("瀑布", "waterfall"),
            ("彩虹", "rainbow"),
            ("星空", "starry sky"),
            ("云朵", "clouds"),
            ("草原", "grassland"),
            ("湖泊", "lake"),
            ("河流", "river"),
            ("树木", "trees"),
            ("鸟类", "birds"),
            ("蝴蝶", "butterfly"),
            ("阳光", "sunshine"),
            ("月亮", "moon"),
            ("海滩", "beach"),
            ("岩石", "rocks"),
            ("田野", "fields"),
            ("花园", "garden")
        ]

        # 创建网格布局来平铺显示选项
        grid_layout = QGridLayout()
        grid_layout.setSpacing(10)

        row = 0
        col = 0
        max_cols = 3  # 每行最多3个选项

        for chinese_text, english_value in prompt_options:
            radio_btn = QRadioButton(chinese_text)
            radio_btn.setObjectName("prompt_radio")
            radio_btn.setProperty("prompt_value", english_value)

            # 设置默认选中
            if (self.selected_prompt == "随机" and english_value == "random") or \
               (self.selected_prompt != "随机" and self.selected_prompt == english_value):
                radio_btn.setChecked(True)

            self.button_group.addButton(radio_btn)
            grid_layout.addWidget(radio_btn, row, col)

            col += 1
            if col >= max_cols:
                col = 0
                row += 1

        scroll_layout.addLayout(grid_layout)

        # 自定义输入区域
        custom_group = QGroupBox("自定义提示词")
        custom_group.setObjectName("custom_group")
        custom_layout = QVBoxLayout()

        self.custom_radio = QRadioButton("使用自定义提示词")
        self.custom_radio.setObjectName("custom_radio")
        self.button_group.addButton(self.custom_radio)

        self.custom_input = QLineEdit()
        self.custom_input.setObjectName("custom_input")
        self.custom_input.setPlaceholderText("请输入自定义提示词（支持中文和英文）")
        self.custom_input.setText(self.custom_prompt)

        # 如果当前选择的是自定义提示词
        if self.selected_prompt not in [opt[1] for opt in prompt_options] and self.selected_prompt != "随机":
            self.custom_radio.setChecked(True)
            self.custom_input.setText(self.selected_prompt)

        custom_layout.addWidget(self.custom_radio)
        custom_layout.addWidget(self.custom_input)
        custom_group.setLayout(custom_layout)

        scroll_layout.addWidget(custom_group)
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        cancel_btn = QPushButton("取消")
        cancel_btn.setObjectName("cancel_btn")
        cancel_btn.clicked.connect(self.reject)

        save_btn = QPushButton("保存")
        save_btn.setObjectName("save_btn")
        save_btn.clicked.connect(self.save_settings)

        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(save_btn)
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)
        self.apply_styles()

    def save_settings(self):
        """保存设置"""
        # 显示提示信息，不实际保存设置
        QMessageBox.information(self, "提示", "此功能仅为疾风团队开发人员测试使用，敬请期待！")

        # 直接关闭对话框，不保存任何设置
        self.reject()

    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QDialog {
                background: #1a1a2e;
                color: white;
            }

            #settings_title {
                color: #bb86fc;
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
            }

            #scroll_area {
                border: 1px solid #444;
                border-radius: 8px;
                background: #2a2a3e;
            }

            #prompt_radio, #custom_radio {
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 8px;
                margin: 5px 2px;
                spacing: 8px;
            }

            #prompt_radio::indicator, #custom_radio::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #666;
                border-radius: 9px;
                background: #2a2a3e;
                margin-right: 8px;
            }

            #prompt_radio::indicator:hover, #custom_radio::indicator:hover {
                border-color: #bb86fc;
                background: rgba(187, 134, 252, 0.2);
                box-shadow: 0 0 5px rgba(187, 134, 252, 0.3);
            }

            #prompt_radio::indicator:checked, #custom_radio::indicator:checked {
                background: #bb86fc;
                border-color: #bb86fc;
                box-shadow: 0 0 8px rgba(187, 134, 252, 0.5);
            }

            #prompt_radio::indicator:checked:hover, #custom_radio::indicator:checked:hover {
                background: #9c64ff;
                border-color: #9c64ff;
                box-shadow: 0 0 10px rgba(156, 100, 255, 0.6);
            }

            #custom_group {
                border: 2px solid #444;
                border-radius: 10px;
                margin-top: 15px;
                padding: 15px;
                color: white;
                font-weight: bold;
                font-size: 14px;
                background: rgba(42, 42, 62, 0.3);
            }

            #custom_group::title {
                color: #bb86fc;
                font-weight: bold;
                font-size: 15px;
                padding: 0 10px;
                background: #1a1a2e;
            }

            #custom_input {
                background: #2a2a3e;
                color: white;
                border: 2px solid #666;
                border-radius: 8px;
                padding: 10px 12px;
                font-size: 14px;
                font-weight: bold;
                margin-top: 8px;
                min-height: 20px;
            }

            #custom_input:hover {
                border-color: #888;
                background: rgba(42, 42, 62, 0.8);
            }

            #custom_input:focus {
                border-color: #bb86fc;
                background: #2a2a3e;
                box-shadow: 0 0 8px rgba(187, 134, 252, 0.3);
                outline: none;
            }

            #cancel_btn, #save_btn {
                background: #533483;
                color: white;
                border: 1px solid #bb86fc;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                min-width: 80px;
            }

            #cancel_btn:hover, #save_btn:hover {
                background: #6a4c93;
                border-color: #cfcfcf;
            }

            #save_btn {
                background: #4a90e2;
                border-color: #357abd;
            }

            #save_btn:hover {
                background: #5ba0f2;
                border-color: #4a90e2;
            }
        """)


class VideoProcessor(QThread):
    progress_updated = pyqtSignal(int, str)
    process_finished = pyqtSignal(bool, str)
    
    def __init__(self, ffmpeg_path, a_video_list, b_video_list, output_dir, config_code, thread_count=1, delete_used_b=False):
        super().__init__()
        self.ffmpeg_path = ffmpeg_path
        self.a_video_list = a_video_list
        self.b_video_list = b_video_list
        self.output_dir = output_dir
        self.config_code = config_code
        self.thread_count = thread_count
        self.delete_used_b = delete_used_b  # 是否删除已用B视频素材
        self.is_cancelled = False
        self.current_processes = []
        self.current_batch_info = ""
        self.longest_video_duration = 0
        self.used_b_videos = set()  # 记录已使用的B视频路径
        
    def get_duration(self, path):
        """获取视频时长（秒）"""
        try:
            result = subprocess.run(
                [self.ffmpeg_path.replace("ffmpeg.exe", "ffprobe.exe"), "-v", "error",
                 "-show_entries", "format=duration", "-of", "default=noprint_wrappers=1:nokey=1", path],
                stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            return float(result.stdout.strip())
        except:
            return 0

    def get_video_resolution(self, path):
        """获取视频分辨率"""
        try:
            ffprobe_path = self.ffmpeg_path.replace("ffmpeg.exe", "ffprobe.exe")
            result = subprocess.run(
                [ffprobe_path, "-v", "quiet", "-select_streams", "v:0",
                 "-show_entries", "stream=width,height", "-of", "csv=s=x:p=0", path],
                stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True,
                creationflags=subprocess.CREATE_NO_WINDOW, timeout=5
            )

            if result.returncode == 0 and result.stdout.strip():
                resolution = result.stdout.strip()
                if 'x' in resolution:
                    width, height = resolution.split('x')
                    return int(width), int(height)

            return 1080, 1920  # 默认分辨率
        except Exception as e:
            print(f"获取视频分辨率失败: {e}")
            return 1080, 1920
    
    def _get_config_param(self, cmd_key, default=None):
        """从配置代码中获取参数"""
        if not self.config_code:
            return default
        
        try:
            if isinstance(self.config_code, str):
                config = json.loads(self.config_code)
            else:
                config = self.config_code
                
            return config.get(cmd_key, default)
        except Exception as e:
            print(f"解析配置代码失败: {str(e)}")
            return default
    
    def run(self):
        try:
            print("开始处理视频...")

            # 验证配置代码
            if not self.config_code:
                self.process_finished.emit(False, "未提供配置代码，无法执行处理")
                return

            try:
                if isinstance(self.config_code, str):
                    config = json.loads(self.config_code)
                else:
                    config = self.config_code

                required_params = ["cmd1_params", "cmd2_params", "cmd3_params", "cmd4_params", "cmd5_params", "cmd6_params"]
                if not all(key in config for key in required_params):
                    self.process_finished.emit(False, "配置代码不完整，缺少必要参数")
                    return
            except json.JSONDecodeError as e:
                self.process_finished.emit(False, f"配置代码格式无效: {str(e)}")
                return

            # 检查视频列表
            if not self.a_video_list or not self.b_video_list:
                self.process_finished.emit(False, "视频列表为空")
                return

            # 批次处理主视频 - B视频循环配对
            total_a_videos = len(self.a_video_list)
            total_b_videos = len(self.b_video_list)

            if total_b_videos == 0:
                self.process_finished.emit(False, "B视频列表为空")
                return

            processed_videos = 0

            # 按批次处理A视频
            for batch_start in range(0, total_a_videos, self.thread_count):
                if self.is_cancelled:
                    return

                batch_end = min(batch_start + self.thread_count, total_a_videos)
                current_batch = self.a_video_list[batch_start:batch_end]

                # 找到当前批次中时长最长的视频用于进度显示
                longest_duration = 0
                longest_video_name = ""
                for video_info in current_batch:
                    if video_info['duration'] > longest_duration:
                        longest_duration = video_info['duration']
                        longest_video_name = os.path.splitext(video_info['filename'])[0]

                batch_info = f"批次 {batch_start//self.thread_count + 1}/{(total_a_videos + self.thread_count - 1)//self.thread_count}"

                # 设置当前批次信息，用于进度显示
                self.current_batch_info = f"{batch_info} - 处理 {len(current_batch)} 个A视频 (最长视频: {longest_video_name})"
                self.longest_video_duration = longest_duration

                self.progress_updated.emit(0, f"开始{self.current_batch_info}")

                # 处理当前批次
                success = self.process_batch(current_batch, batch_info)
                if not success:
                    return

                processed_videos += len(current_batch)

            # 处理完成后删除已使用的B视频素材
            if self.delete_used_b and self.used_b_videos:
                self.progress_updated.emit(95, "正在删除已用B视频素材...")
                self.delete_used_b_videos()

            self.progress_updated.emit(100, "所有视频处理完成")
            success_msg = f"成功处理了 {total_a_videos} 个A视频，B视频循环配对"
            if self.delete_used_b and self.used_b_videos:
                success_msg += f"，已删除 {len(self.used_b_videos)} 个已用B视频素材"
            self.process_finished.emit(True, success_msg)

        except Exception as e:
            self.process_finished.emit(False, f"处理过程中发生错误: {str(e)}")

    def process_batch(self, batch_videos, batch_info):
        """处理一个批次的视频 - B视频循环配对"""
        import threading
        import queue
        from concurrent.futures import ThreadPoolExecutor, as_completed

        # 创建A视频与B视频的循环配对
        combinations = []
        total_b_videos = len(self.b_video_list)

        for i, a_video_info in enumerate(batch_videos):
            # B视频循环配对：使用模运算实现循环
            b_index = i % total_b_videos
            b_video_info = self.b_video_list[b_index]
            combinations.append((a_video_info, b_video_info))
            print(f"配对: A视频[{i}] {os.path.splitext(a_video_info['filename'])[0]} -> B视频[{b_index}] {os.path.splitext(b_video_info['filename'])[0]}")

        print(f"开始处理批次: {len(combinations)} 个A视频，B视频循环配对")

        # 使用线程池限制并发数
        success_count = 0
        with ThreadPoolExecutor(max_workers=self.thread_count) as executor:
            # 提交所有任务
            future_to_combination = {}
            for a_video_info, b_video_info in combinations:
                if self.is_cancelled:
                    break

                future = executor.submit(
                    self.process_single_video_combination,
                    a_video_info, b_video_info
                )
                future_to_combination[future] = (a_video_info, b_video_info)

            # 等待任务完成
            for future in as_completed(future_to_combination):
                if self.is_cancelled:
                    break

                a_video_info, b_video_info = future_to_combination[future]
                try:
                    success = future.result()
                    if success:
                        success_count += 1
                        print(f"处理完成: {os.path.splitext(a_video_info['filename'])[0]}_{os.path.splitext(b_video_info['filename'])[0]}")
                    else:
                        print(f"处理失败: {os.path.splitext(a_video_info['filename'])[0]}_{os.path.splitext(b_video_info['filename'])[0]}")
                except Exception as e:
                    print(f"处理异常: {os.path.splitext(a_video_info['filename'])[0]}_{os.path.splitext(b_video_info['filename'])[0]} - {e}")

        print(f"批次处理完成: {success_count}/{len(combinations)} 个视频成功")
        return success_count == len(combinations)

    def process_single_video_combination(self, a_video_info, b_video_info):
        """处理单个视频组合（线程池版本）"""
        try:
            a_video_path = a_video_info['path']
            b_video_path = b_video_info['path']
            a_filename = os.path.splitext(a_video_info['filename'])[0]
            b_filename = os.path.splitext(b_video_info['filename'])[0]

            # 获取主视频分辨率
            width, height = self.get_video_resolution(a_video_path)
            print(f"开始处理组合: {a_filename} ({width}x{height}) + {b_filename}")

            success = self.process_video_combination(
                a_video_path, b_video_path, a_filename, b_filename, width, height
            )
            return success

        except Exception as e:
            print(f"处理组合异常: {e}")
            return False

    def process_single_video_thread(self, a_video_info, b_video_info, result_queue, batch_info):
        """在线程中处理单个视频"""
        try:
            a_video_path = a_video_info['path']
            b_video_path = b_video_info['path']
            a_filename = os.path.splitext(a_video_info['filename'])[0]
            b_filename = os.path.splitext(b_video_info['filename'])[0]

            # 获取主视频分辨率
            width, height = self.get_video_resolution(a_video_path)
            print(f"处理视频: {a_filename}, 分辨率: {width}x{height}")

            success = self.process_video_combination(
                a_video_path, b_video_path, a_filename, b_filename, width, height
            )
            result_queue.put(success)

        except Exception as e:
            print(f"线程处理视频失败: {e}")
            result_queue.put(False)

    def process_video_combination(self, a_video_path, b_video_path, a_filename, b_filename, width, height):
        """处理单个视频组合"""
        try:
            # 创建工作目录
            work_dir = self.output_dir
            os.makedirs(work_dir, exist_ok=True)

            # 定义临时文件路径
            a_60 = os.path.join(work_dir, f"temp_A_{a_filename}_60fps.mp4")
            b_loop = os.path.join(work_dir, f"temp_B_{b_filename}_60fps_looped.mp4")
            temp_video = os.path.join(work_dir, f"temp_{a_filename}_{b_filename}_video.mp4")
            temp_audio = os.path.join(work_dir, f"temp_{a_filename}_audio.m4a")
            final = os.path.join(work_dir, f"temp_{a_filename}_{b_filename}_final.mp4")
            compressed = os.path.join(work_dir, f"{a_filename}_{b_filename}_翻牌成功.mp4")

            # 1. 获取 A 视频时长
            duration = self.get_duration(a_video_path)
            if duration <= 0:
                print(f"无法获取A视频时长: {a_video_path}")
                return False

            # 计算当前视频在最长视频中的进度权重
            progress_weight = duration / self.longest_video_duration if self.longest_video_duration > 0 else 1.0

            def update_progress(step_progress, step_name):
                """更新进度，基于最长视频的处理进度"""
                if self.longest_video_duration > 0:
                    # 根据当前视频时长调整进度
                    adjusted_progress = int(step_progress * progress_weight)
                    self.progress_updated.emit(adjusted_progress, f"{self.current_batch_info} - {step_name}")

            update_progress(5, f"开始处理 {a_filename}_{b_filename}")
            
            # 2. 处理 A 视频 -> 60fps CFR
            update_progress(10, f"正在处理主视频: {a_filename}")
            if self.is_cancelled:
                return False

            cmd1_params = self._get_config_param("cmd1_params")
            if not cmd1_params or len(cmd1_params) < 3:
                print("配置参数cmd1_params无效")
                return False

            # 使用默认编码器
            cmd1 = [
                self.ffmpeg_path, "-y",
                "-i", a_video_path,
                "-vf", f"scale={width}:{height}:force_original_aspect_ratio=decrease,pad={width}:{height}:(ow-iw)/2:(oh-ih)/2,fps={cmd1_params[0]}",
                "-c:v", "libx264",
                "-profile:v", "high",
                "-level", "6.2",
                "-refs", "2",
                "-bf", "0",
                "-crf", cmd1_params[2],
                "-preset", cmd1_params[1],
                "-pix_fmt", "yuv420p"
            ]

            cmd1.extend(["-an", a_60])

            if not self._run_command(cmd1, "处理A视频"):
                return False
                
            # 3. 处理 B 视频循环
            update_progress(30, f"正在处理辅助视频: {b_filename}")
            if self.is_cancelled:
                return False
                
            cmd2_params = self._get_config_param("cmd2_params")
            if not cmd2_params or len(cmd2_params) < 3:
                print("配置参数cmd2_params无效")
                return False

            # 使用默认编码器
            cmd2 = [
                self.ffmpeg_path, "-y",
                "-stream_loop", "-1",
                "-i", b_video_path,
                "-t", str(duration),
                "-vf", f"scale={width}:{height}:force_original_aspect_ratio=decrease,pad={width}:{height}:(ow-iw)/2:(oh-ih)/2,fps={cmd2_params[0]}",
                "-c:v", "libx264",
                "-profile:v", "high",
                "-level", "6.2",
                "-refs", "2",
                "-bf", "0",
                "-crf", cmd2_params[2],
                "-preset", cmd2_params[1],
                "-pix_fmt", "yuv420p"
            ]

            cmd2.extend(["-an", b_loop])
            
            if not self._run_command(cmd2, "处理B视频"):
                return False
                
            # 4. 交错合成
            update_progress(50, f"正在进行防封合成: {a_filename}_{b_filename}")
            if self.is_cancelled:
                return False
                
            cmd3_params = self._get_config_param("cmd3_params")
            if not cmd3_params or len(cmd3_params) < 4:
                print("配置参数cmd3_params无效")
                return False

            # 使用默认编码器
            cmd3 = [
                self.ffmpeg_path, "-y",
                "-i", a_60,
                "-i", b_loop,
                "-filter_complex", cmd3_params[0],
                "-map", "[v]",
                "-c:v", "libx264",
                "-profile:v", "high",
                "-level", "6.2",
                "-refs", "2",
                "-bf", "0",
                "-crf", cmd3_params[2],
                "-preset", cmd3_params[1],
                "-pix_fmt", "yuv420p"
            ]

            cmd3.extend([
                "-vsync", "vfr",
                "-video_track_timescale", cmd3_params[3],
                "-an", temp_video
            ])
            
            if not self._run_command(cmd3, "交错合成"):
                return False
                
            # 5. 提取音频
            update_progress(65, f"正在提取音频去重: {a_filename}")
            if self.is_cancelled:
                return False
                
            cmd4_params = self._get_config_param("cmd4_params")
            if not cmd4_params or len(cmd4_params) < 3:
                print("配置参数cmd4_params无效")
                return False

            cmd4 = [
                self.ffmpeg_path, "-y",
                "-i", a_video_path,
                "-vn", "-c:a", "aac",
                "-b:a", cmd4_params[0],
                "-ar", cmd4_params[1],
                "-ac", cmd4_params[2],
                temp_audio
            ]
            
            if not self._run_command(cmd4, "提取音频"):
                return False
                
            # 6. 混流封装
            update_progress(80, f"正在最后的混流封装: {a_filename}_{b_filename}")
            if self.is_cancelled:
                return False
                
            cmd5_params = self._get_config_param("cmd5_params")
            if not cmd5_params or len(cmd5_params) < 1:
                print("配置参数cmd5_params无效")
                return False

            cmd5 = [
                self.ffmpeg_path, "-y",
                "-i", temp_video,
                "-i", temp_audio,
                "-map", "0:v", "-map", "1:a",
                "-c", "copy",
                "-movflags", cmd5_params[0],
                "-map_metadata", "-1",
                "-metadata", "comment=",
                "-metadata:s:v", "language=und",
                final
            ]
            
            if not self._run_command(cmd5, "混流封装"):
                return False
                
            # 7. 最终压缩
            update_progress(90, f"正在最终压缩: {a_filename}_{b_filename}")
            if self.is_cancelled:
                return False
                
            cmd6_params = self._get_config_param("cmd6_params")
            if not cmd6_params or len(cmd6_params) < 2:
                print("配置参数cmd6_params无效")
                return False

            # 使用默认编码器
            cmd6 = [
                self.ffmpeg_path, "-y",
                "-i", final,
                "-c:v", "libx264",
                "-preset", cmd6_params[0],
                "-crf", cmd6_params[1]
            ]

            cmd6.extend(["-c:a", "copy", compressed])
            
            if not self._run_command(cmd6, "最终压缩"):
                return False
                
            # 清理临时文件
            update_progress(95, f"正在清理临时文件: {a_filename}_{b_filename}")
            for temp_file in [a_60, b_loop, temp_video, temp_audio, final]:
                try:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                except:
                    pass

            update_progress(100, f"完成: {a_filename}_{b_filename}_翻牌成功.mp4")
            print(f"视频处理完成: {a_filename}_{b_filename}_翻牌成功.mp4")

            # 记录已使用的B视频
            if self.delete_used_b:
                self.used_b_videos.add(b_video_path)
                print(f"记录已使用的B视频: {b_video_path}")

            return True

        except Exception as e:
            print(f"处理组合失败 {a_filename}_{b_filename}: {str(e)}")
            return False
    
    def _run_command(self, cmd, step_name):
        """执行FFmpeg命令"""
        try:
            process = subprocess.Popen(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                text=True, encoding='utf-8', errors='ignore',
                creationflags=subprocess.CREATE_NO_WINDOW
            )

            # 添加到进程列表
            self.current_processes.append(process)

            stdout, stderr = process.communicate()

            # 从进程列表移除
            if process in self.current_processes:
                self.current_processes.remove(process)

            if process.returncode != 0:
                print(f"{step_name}失败: {stderr}")
                return False

            return True
        except Exception as e:
            print(f"{step_name}异常: {str(e)}")
            return False
    
    def delete_used_b_videos(self):
        """删除已使用的B视频素材"""
        deleted_count = 0
        failed_count = 0

        for b_video_path in self.used_b_videos:
            try:
                if os.path.exists(b_video_path):
                    os.remove(b_video_path)
                    deleted_count += 1
                    print(f"已删除B视频素材: {b_video_path}")
                else:
                    print(f"B视频素材不存在，跳过: {b_video_path}")
            except Exception as e:
                failed_count += 1
                print(f"删除B视频素材失败: {b_video_path}, 错误: {e}")

        print(f"B视频素材删除完成: 成功删除 {deleted_count} 个，失败 {failed_count} 个")

    def cancel(self):
        """取消处理"""
        self.is_cancelled = True
        for process in self.current_processes:
            try:
                if process and process.poll() is None:
                    process.terminate()
            except:
                pass
        self.current_processes.clear()


class MainWindow(QMainWindow):
    def __init__(self, expiry_time_str, card_key, mute=False, aes_key="", aes_iv=""):
        super().__init__()
        self.expiry_time_str = expiry_time_str
        self.card_key = card_key
        self.mute = mute
        self.aes_key = aes_key
        self.aes_iv = aes_iv
        self.ffmpeg_path = ""
        self.mkvmerge_path = ""
        self.a_video_folder = ""
        self.b_video_folder = ""
        self.a_video_list = []
        self.b_video_list = []
        self.output_dir = ""
        self.is_processing = False
        self.worker = None
        self.decrypted_code = None

        # 聊天室窗口
        self.chatroom_window = None

        # 初始化单视频模式相关变量
        self.selected_prompt = "随机"  # 默认选择随机
        self.custom_prompt = ""  # 自定义提示词

        # 打印AES密钥信息
        if self.aes_key and self.aes_iv:
            print(f"AES1: {self.aes_key}")
            print(f"AES2: {self.aes_iv}")

        self.setup_ui()
        self.setup_style()

        # 初始化UI状态（在所有UI组件创建完成后）
        self.update_b_folder_ui()

        # 自动检测ffmpeg路径
        self.auto_detect_ffmpeg()

        # 启动到期时间检查定时器
        self.setup_expiry_timer()

        # 设置聊天室按钮震动动画
        self.setup_chatroom_animation()

    def setup_expiry_timer(self):
        """设置到期时间检查定时器"""
        from PyQt5.QtCore import QTimer

        self.expiry_timer = QTimer()
        self.expiry_timer.timeout.connect(self.check_expiry)
        self.expiry_timer.start(60000)  # 每分钟检查一次

        # 立即检查一次
        self.check_expiry()

    def check_expiry(self):
        """检查是否到期"""
        try:
            from datetime import datetime

            # 解析到期时间
            expiry_time = datetime.strptime(self.expiry_time_str, "%Y-%m-%d %H:%M:%S")
            current_time = datetime.now()

            if current_time >= expiry_time:
                QMessageBox.critical(
                    self, "授权到期",
                    f"您的授权已于 {self.expiry_time_str} 到期\n\n程序将自动退出，请联系客服续费。"
                )
                QApplication.quit()

        except Exception as e:
            print(f"检查到期时间失败: {e}")
            # 如果无法解析到期时间，为安全起见退出程序
            QMessageBox.critical(
                self, "授权验证失败",
                "无法验证授权状态，程序将退出。\n\n请联系客服处理。"
            )
            QApplication.quit()

    def setup_chatroom_animation(self):
        """设置聊天室按钮震动动画"""
        # 创建位置动画
        self.chatroom_animation = QPropertyAnimation(self.chatroom_btn, b"geometry")
        self.chatroom_animation.setDuration(100)  # 100ms的震动
        self.chatroom_animation.setEasingCurve(QEasingCurve.InOutQuad)

        # 震动计数器
        self.shake_count = 0
        self.max_shakes = 4  # 两次震动，每次震动包含左右各2次移动

        # 动画完成时的回调
        self.chatroom_animation.finished.connect(self.on_shake_finished)

        # 延迟启动第一次震动（窗口完全显示后）
        QTimer.singleShot(1000, self.start_chatroom_shake)

    def start_chatroom_shake(self):
        """开始聊天室按钮震动"""
        if self.shake_count >= self.max_shakes:
            return

        # 获取按钮当前位置
        current_rect = self.chatroom_btn.geometry()

        # 设置震动的起始和结束位置
        if self.shake_count % 2 == 0:
            # 向右震动
            start_rect = current_rect
            end_rect = QRect(current_rect.x() + 3, current_rect.y(), current_rect.width(), current_rect.height())
        else:
            # 向左震动
            start_rect = current_rect
            end_rect = QRect(current_rect.x() - 3, current_rect.y(), current_rect.width(), current_rect.height())

        # 设置动画
        self.chatroom_animation.setStartValue(start_rect)
        self.chatroom_animation.setEndValue(end_rect)

        # 启动动画
        self.chatroom_animation.start()

        self.shake_count += 1

    def on_shake_finished(self):
        """震动动画完成回调"""
        if self.shake_count < self.max_shakes:
            # 继续下一次震动
            QTimer.singleShot(58, self.start_chatroom_shake)
        elif self.shake_count == self.max_shakes:
            # 第一轮震动完成，等待3秒后开始第二轮
            QTimer.singleShot(3000, self.start_second_shake_round)

    def start_second_shake_round(self):
        """开始第二轮震动"""
        self.shake_count = 0  # 重置计数器
        self.max_shakes = 4   # 第二轮也是4次震动
        self.start_chatroom_shake()

    def setup_ui(self):
        """设置UI界面"""
        self.setWindowTitle(f"{SOFTWARE_NAME} {SOFTWARE_VERSION} ({self.expiry_time_str})")
        self.setGeometry(100, 100, 600, 500)

        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # 顶部布局：功能选择在左上角，聊天室按钮在右上角
        top_layout = QHBoxLayout()

        # 功能选择（左上角）- 改为二级分类选择
        function_area = QHBoxLayout()
        function_label = QLabel("平台选择:")
        function_label.setObjectName("function_label")
        function_area.addWidget(function_label)

        # 平台选择下拉框
        self.platform_combo = QComboBox()
        self.platform_combo.setObjectName("platform_combo")
        self.platform_combo.addItem("⚡ 疾风单A (DY)", "jf_single_dy_platform")
        self.platform_combo.addItem("🎵 抖音 (DY)", "dy_platform")
        self.platform_combo.addItem("📱 快手 (KS)", "ks_platform")
        self.platform_combo.addItem("🌍 TikTok (TK)", "tk_platform")
        self.platform_combo.addItem("📺 视频号 (SPH)", "sph_platform")
        self.platform_combo.setCurrentIndex(0)  # 默认选择疾风单A
        self.platform_combo.currentIndexChanged.connect(self.on_platform_changed)
        function_area.addWidget(self.platform_combo)

        # 功能选择下拉框
        function_label2 = QLabel("功能选择:")
        function_label2.setObjectName("function_label")
        function_area.addWidget(function_label2)

        self.function_combo = QComboBox()
        self.function_combo.setObjectName("function_combo")
        function_area.addWidget(self.function_combo)

        # 初始化功能选项（在所有组件创建后）
        self.init_function_options()

        # 连接信号（在初始化后连接，避免初始化时触发）
        self.function_combo.currentTextChanged.connect(self.on_function_changed)
        self.function_combo.currentIndexChanged.connect(self.update_b_folder_ui)

        top_layout.addLayout(function_area)
        top_layout.addStretch()  # 中间空白区域

        # 聊天室按钮区域（右上角）
        chatroom_area = QVBoxLayout()
        chatroom_area.setSpacing(2)

        # 聊天室按钮
        self.chatroom_btn = QPushButton("💬 发财聊天室")
        self.chatroom_btn.setObjectName("chatroom_btn")
        self.chatroom_btn.clicked.connect(self.open_chatroom)
        self.chatroom_btn.setFixedSize(140, 40)

        # 聊天室提示文字
        chatroom_tip = QLabel("获取更新/反馈/交流/发大财~")
        chatroom_tip.setObjectName("chatroom_tip")
        chatroom_tip.setAlignment(Qt.AlignCenter)

        chatroom_area.addWidget(self.chatroom_btn)
        chatroom_area.addWidget(chatroom_tip)

        top_layout.addLayout(chatroom_area)

        main_layout.addLayout(top_layout)

        # 视频选择区域
        video_group = QGroupBox("视频文件选择")
        video_group.setObjectName("group")
        video_layout = QVBoxLayout(video_group)

        # A视频文件夹选择
        a_layout = QHBoxLayout()
        self.a_label = QLabel("A视频文件夹 (主视频): 未选择")
        self.a_label.setObjectName("path_label")
        self.a_label.setStyleSheet("color: #888888;")  # 未选择时为灰色
        self.a_btn = QPushButton("选择A视频文件夹")
        self.a_btn.setObjectName("select_btn")
        self.a_btn.clicked.connect(self.select_a_video_folder)

        self.a_detail_btn = QPushButton("详情")
        self.a_detail_btn.setObjectName("detail_btn")
        self.a_detail_btn.clicked.connect(self.show_a_video_list)
        self.a_detail_btn.setEnabled(False)
        self.a_detail_btn.setFixedSize(60, 30)

        a_layout.addWidget(self.a_label)
        a_layout.addWidget(self.a_btn)
        a_layout.addWidget(self.a_detail_btn)
        video_layout.addLayout(a_layout)

        # B视频文件夹选择
        b_layout = QHBoxLayout()
        self.b_label = QLabel("B视频文件夹 (循环视频): 未选择")
        self.b_label.setObjectName("path_label")
        self.b_label.setStyleSheet("color: #888888;")  # 未选择时为灰色
        self.b_btn = QPushButton("选择B视频文件夹")
        self.b_btn.setObjectName("select_btn")
        self.b_btn.clicked.connect(self.select_b_video_folder)

        # 单视频推流设置按钮（初始隐藏）
        self.single_video_btn = QPushButton("单视频推流设置")
        self.single_video_btn.setObjectName("single_video_btn")
        self.single_video_btn.clicked.connect(self.show_single_video_settings)
        self.single_video_btn.setVisible(False)

        # 推流词显示标签（初始隐藏）
        self.prompt_display_label = QLabel("")
        self.prompt_display_label.setObjectName("prompt_display_label")
        self.prompt_display_label.setVisible(False)

        self.b_detail_btn = QPushButton("详情")
        self.b_detail_btn.setObjectName("detail_btn")
        self.b_detail_btn.clicked.connect(self.show_b_video_list)
        self.b_detail_btn.setEnabled(False)
        self.b_detail_btn.setFixedSize(60, 30)

        b_layout.addWidget(self.b_label)
        b_layout.addWidget(self.b_btn)
        b_layout.addWidget(self.single_video_btn)
        b_layout.addWidget(self.prompt_display_label)
        b_layout.addWidget(self.b_detail_btn)
        video_layout.addLayout(b_layout)

        main_layout.addWidget(video_group)

        # 输出目录选择
        output_group = QGroupBox("输出目录")
        output_group.setObjectName("group")
        output_layout = QHBoxLayout(output_group)

        self.output_label = QLabel("未选择输出目录")
        self.output_label.setObjectName("path_label")
        self.output_btn = QPushButton("选择输出目录")
        self.output_btn.setObjectName("select_btn")
        self.output_btn.clicked.connect(self.select_output_dir)

        output_layout.addWidget(self.output_label)
        output_layout.addWidget(self.output_btn)
        main_layout.addWidget(output_group)

        # 处理设置
        settings_group = QGroupBox("处理设置")
        settings_group.setObjectName("group")
        settings_layout = QHBoxLayout(settings_group)

        # 线程数设置
        thread_label = QLabel("处理线程数:")
        thread_label.setObjectName("setting_label")

        self.thread_spinbox = QSpinBox()
        self.thread_spinbox.setMinimum(1)
        self.thread_spinbox.setMaximum(8)
        self.thread_spinbox.setValue(1)
        self.thread_spinbox.setObjectName("thread_spinbox")
        self.thread_spinbox.setToolTip("同时处理的视频数量，建议根据电脑性能调整（推荐默认1）")

        settings_layout.addWidget(thread_label)
        settings_layout.addWidget(self.thread_spinbox)

        # B素材删除选项（移动到处理线程数后面）
        self.delete_used_b_checkbox = QCheckBox("删除已用B视频素材")
        self.delete_used_b_checkbox.setObjectName("delete_b_checkbox")
        self.delete_used_b_checkbox.setChecked(False)  # 默认不勾选
        self.delete_used_b_checkbox.setToolTip("勾选后，处理完成的B视频素材将被自动删除")
        settings_layout.addWidget(self.delete_used_b_checkbox)

        # 单视频模式勾选项（移动到处理设置区域）
        self.single_video_checkbox = QCheckBox("单视频模式（独家·新）")
        self.single_video_checkbox.setObjectName("single_video_checkbox")
        self.single_video_checkbox.setChecked(False)
        self.single_video_checkbox.setToolTip("勾选后使用疾风自研单视频")
        self.single_video_checkbox.stateChanged.connect(self.on_single_video_mode_changed)
        settings_layout.addWidget(self.single_video_checkbox)

        settings_layout.addStretch()

        main_layout.addWidget(settings_group)



        # 控制按钮
        control_layout = QHBoxLayout()

        self.start_btn = QPushButton("开始处理")
        self.start_btn.setObjectName("start_btn")
        self.start_btn.clicked.connect(self.start_processing)

        self.stop_btn = QPushButton("停止处理")
        self.stop_btn.setObjectName("stop_btn")
        self.stop_btn.clicked.connect(self.stop_processing)
        self.stop_btn.setEnabled(False)

        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        main_layout.addLayout(control_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("progress")
        main_layout.addWidget(self.progress_bar)

        # 状态标签
        self.status_label = QLabel("软件后续更新将同步发送至发财聊天室（右上角功能）")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setObjectName("status")
        main_layout.addWidget(self.status_label)

        # 八秒处理区域
        duration_group = self.setup_duration_patch_ui()
        main_layout.addWidget(duration_group)

    def setup_style(self):
        """设置黑紫风格样式"""
        self.setStyleSheet("""
            QMainWindow {
                background: #1a1a2e;
            }

            QWidget {
                background: transparent;
                color: white;
                font-family: "Microsoft YaHei", "微软雅黑";
                font-size: 12px;
            }

            #title {
                font-size: 24px;
                font-weight: bold;
                color: #e94560;
                margin: 20px 0;
            }

            #chatroom_btn {
                background: #ffd700;
                color: #1a1a2e;
                border: 1px solid #ffd700;
                border-radius: 4px;
                padding: 5px 10px;
                font-size: 16px;
                font-weight: bold;
            }

            #chatroom_btn:hover {
                background: #ffed4e;
                border: 1px solid #ffed4e;
            }

            #chatroom_btn:pressed {
                background: #e6c200;
            }

            #chatroom_tip {
                color: #ffd700;
                font-size: 10px;
                font-weight: bold;
                margin: 0;
                padding: 0;
            }

            #setting_label {
                color: white;
                font-size: 12px;
                margin-right: 10px;
            }

            #thread_spinbox {
                background: #444;
                color: white;
                border: 1px solid #666;
                border-radius: 4px;
                padding: 5px;
                font-size: 12px;
                min-width: 60px;
            }

            #thread_spinbox:focus {
                border-color: #6200ea;
            }

            QGroupBox {
                font-weight: bold;
                border: 2px solid #533483;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background: rgba(83, 52, 131, 0.1);
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #bb86fc;
                font-size: 14px;
            }

            #path_label {
                color: #cfcfcf;
                font-size: 14px;
                padding: 8px;
                background: rgba(255,255,255,0.05);
                border: 1px solid #444;
                border-radius: 4px;
            }

            #select_btn {
                background: #6200ea;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 16px;
                min-width: 120px;
            }

            #select_btn:hover {
                background: #7c4dff;
            }

            #select_btn:pressed {
                background: #3700b3;
            }

            #detail_btn {
                background: #ff9800;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 5px 10px;
                font-weight: bold;
                font-size: 11px;
            }

            #detail_btn:hover {
                background: #ffb74d;
            }

            #detail_btn:pressed {
                background: #f57c00;
            }

            #detail_btn:disabled {
                background: #666;
                color: #999;
            }

            #start_btn {
                background: #e91e63;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 30px;
                font-weight: bold;
                font-size: 14px;
                min-width: 150px;
            }

            #start_btn:hover {
                background: #f06292;
            }

            #start_btn:pressed {
                background: #ad1457;
            }

            #start_btn:disabled {
                background: #444;
                color: #888;
            }

            #stop_btn {
                background: #ff5722;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 30px;
                font-weight: bold;
                font-size: 14px;
                min-width: 150px;
            }

            #stop_btn:hover {
                background: #ff7043;
            }

            #stop_btn:pressed {
                background: #d84315;
            }

            #stop_btn:disabled {
                background: #444;
                color: #888;
            }

            #progress {
                border: 2px solid #533483;
                border-radius: 8px;
                text-align: center;
                background: rgba(255,255,255,0.1);
                height: 25px;
            }

            #progress::chunk {
                background: #bb86fc;
                border-radius: 6px;
            }

            #status {
                color: #bb86fc;
                font-size: 14px;
                font-weight: bold;
                padding: 10px;
                background: rgba(187, 134, 252, 0.1);
                border: 1px solid #533483;
                border-radius: 6px;
            }

            #function_label {
                color: white;
                font-size: 18px;
                font-weight: bold;
                margin-right: 16px;
            }

            #platform_combo {
                background: #533483;
                color: white;
                border: 1px solid #bb86fc;
                border-radius: 6px;
                padding: 8px 15px;
                font-size: 14px;
                font-weight: bold;
                min-width: 150px;
            }

            #function_combo {
                background: #533483;
                color: white;
                border: 1px solid #bb86fc;
                border-radius: 6px;
                padding: 8px 15px;
                font-size: 14px;
                font-weight: bold;
                min-width: 200px;
            }

            #platform_combo:hover, #function_combo:hover {
                background: #6a4c93;
                border-color: #cfcfcf;
            }

            #platform_combo:focus, #function_combo:focus {
                border-color: #ffd700;
            }

            #platform_combo::drop-down, #function_combo::drop-down {
                border: none;
                width: 20px;
            }

            #platform_combo::down-arrow, #function_combo::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid white;
                margin-right: 5px;
            }

            #platform_combo QAbstractItemView, #function_combo QAbstractItemView {
                background: #533483;
                color: white;
                border: 1px solid #bb86fc;
                selection-background-color: #bb86fc;
                selection-color: #1a1a2e;
                outline: none;
            }

            #platform_combo QAbstractItemView::item, #function_combo QAbstractItemView::item {
                padding: 8px 15px;
                border: none;
            }

            #platform_combo QAbstractItemView::item:hover, #function_combo QAbstractItemView::item:hover {
                background: #6a4c93;
            }

            #platform_combo QAbstractItemView::item:selected, #function_combo QAbstractItemView::item:selected {
                background: #bb86fc;
                color: #1a1a2e;
            }

            #duration_group {
                background: rgba(26, 26, 46, 0.9);
                border: 2px solid #533483;
                border-radius: 8px;
                margin: 10px 0;
                padding: 10px;
                font-weight: bold;
                color: white;
            }

            #duration_spinbox {
                background: #444;
                color: white;
                border: 1px solid #666;
                border-radius: 4px;
                padding: 5px;
                font-size: 12px;
                min-width: 80px;
            }

            #duration_spinbox:focus {
                border-color: #6200ea;
            }

            #duration_folder_btn {
                background: #ff9800;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 14px;
                min-width: 150px;
            }

            #duration_folder_btn:hover {
                background: #ffb74d;
            }

            #duration_folder_btn:pressed {
                background: #f57c00;
            }

            #duration_folder_btn:disabled {
                background: #444;
                color: #888;
            }

            #delete_b_checkbox {
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 8px;
                margin: 5px 0;
            }

            #delete_b_checkbox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #666;
                border-radius: 3px;
                background: #2a2a3e;
            }

            #delete_b_checkbox::indicator:hover {
                border-color: #bb86fc;
                background: rgba(187, 134, 252, 0.1);
            }

            #delete_b_checkbox::indicator:checked {
                background: #bb86fc;
                border-color: #bb86fc;
                image: none;
            }

            #delete_b_checkbox::indicator:checked:hover {
                background: #9c64ff;
                border-color: #9c64ff;
            }

            #single_video_checkbox {
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 8px;
                margin: 5px 0;
                margin-left: 20px;
            }

            #single_video_checkbox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #666;
                border-radius: 3px;
                background: #2a2a3e;
            }

            #single_video_checkbox::indicator:hover {
                border-color: #bb86fc;
                background: rgba(187, 134, 252, 0.1);
            }

            #single_video_checkbox::indicator:checked {
                background: #bb86fc;
                border-color: #bb86fc;
                image: none;
            }

            #single_video_checkbox::indicator:checked:hover {
                background: #9c64ff;
                border-color: #9c64ff;
            }

            #single_video_btn {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4a90e2, stop:1 #357abd);
                color: white;
                border: 2px solid #357abd;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 16px;
                font-weight: bold;
                min-width: 160px;
            }

            #single_video_btn:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5ba0f2, stop:1 #4a90e2);
                border-color: #4a90e2;
            }

            #single_video_btn:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #357abd, stop:1 #2a5f8f);
                border-color: #2a5f8f;
            }

            #prompt_display_label {
                color: #bb86fc;
                font-size: 14px;
                font-weight: bold;
                margin-left: 10px;
                padding: 5px;
            }
        """)

    def auto_detect_ffmpeg(self):
        """自动检测bin目录下的关键组件"""
        # 获取程序所在目录
        exe_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
        print(f"程序所在目录: {exe_dir}")
        
        # 检查bin文件夹是否存在
        bin_dir = os.path.join(exe_dir, "bin")
        if not os.path.exists(bin_dir) or not os.path.isdir(bin_dir):
            print("未找到bin文件夹")
            QMessageBox.critical(self, "错误", "关键组件丢失，请尝试重新安装！")
            sys.exit(1)
            
        # 检查bin文件夹中的ffmpeg.exe是否存在
        ffmpeg_exe = os.path.join(bin_dir, "ffmpeg.exe")
        if not os.path.exists(ffmpeg_exe) or not os.path.isfile(ffmpeg_exe):
            print("未找到ffmpeg.exe")
            QMessageBox.critical(self, "错误", "关键组件丢失，请尝试重新安装！")
            sys.exit(1)
            
        # 检查bin文件夹中的mkvmerge.exe是否存在
        mkvmerge_exe = os.path.join(bin_dir, "mkvmerge.exe")
        if not os.path.exists(mkvmerge_exe) or not os.path.isfile(mkvmerge_exe):
            print("未找到mkvmerge.exe")
            QMessageBox.critical(self, "错误", "关键组件丢失，请尝试重新安装！")
            sys.exit(1)
            
        # 检查其他关键文件
        required_files = ["ffprobe.exe", "avcodec-62.dll", "avfilter-11.dll", "avformat-62.dll", "avutil-60.dll", "swresample-6.dll", "swscale-9.dll"]
        missing_files = []
        
        for file in required_files:
            file_path = os.path.join(bin_dir, file)
            if not os.path.exists(file_path) or not os.path.isfile(file_path):
                print(f"未找到关键文件: {file}")
                missing_files.append(file)
                
        if missing_files:
            print(f"缺少关键文件: {', '.join(missing_files)}")
            QMessageBox.critical(self, "错误", "关键组件丢失，请尝试重新安装！")
            sys.exit(1)
            
        # 设置ffmpeg和mkvmerge路径
        self.ffmpeg_path = ffmpeg_exe
        self.mkvmerge_path = mkvmerge_exe
        print(f"自动检测到ffmpeg路径: {self.ffmpeg_path}")
        print(f"自动检测到mkvmerge路径: {self.mkvmerge_path}")
        
        # 验证ffmpeg是否可用
        try:
            result = subprocess.run(
                [ffmpeg_exe, "-version"],
                stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                text=True, encoding="utf-8", errors="ignore",
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            if result.returncode == 0:
                print("ffmpeg验证成功")
            else:
                print(f"ffmpeg验证失败: {result.stderr}")
                QMessageBox.critical(self, "错误", "关键组件验证失败，请尝试重新安装！")
                sys.exit(1)
        except Exception as e:
            print(f"ffmpeg验证异常: {e}")
            QMessageBox.critical(self, "错误", "关键组件验证失败，请尝试重新安装！")
            sys.exit(1)
            
        # 验证mkvmerge是否可用
        try:
            result = subprocess.run(
                [mkvmerge_exe, "--version"],
                stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                text=True, encoding="utf-8", errors="ignore",
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            if result.returncode == 0:
                print("mkvmerge验证成功")
            else:
                print(f"mkvmerge验证失败: {result.stderr}")
                QMessageBox.critical(self, "错误", "MKV工具集验证失败，请尝试重新安装！")
                sys.exit(1)
        except Exception as e:
            print(f"mkvmerge验证异常: {e}")
            QMessageBox.critical(self, "错误", "MKV工具集验证失败，请尝试重新安装！")
            sys.exit(1)

    def select_a_video_folder(self):
        """选择A视频文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择A视频文件夹（主视频）")
        if folder_path:
            self.a_video_folder = folder_path
            self.a_video_list = self.get_video_files_from_folder(folder_path)

            folder_name = os.path.basename(folder_path)
            video_count = len(self.a_video_list)

            if video_count > 0:
                self.a_label.setText(f"A视频文件夹: {folder_name} ({video_count} 个视频)")
                self.a_label.setStyleSheet("color: #00ff00; font-weight: bold;")  # 亮绿色
                self.a_detail_btn.setEnabled(True)
            else:
                self.a_label.setText(f"A视频文件夹: {folder_name} (无视频文件)")
                self.a_label.setStyleSheet("color: #ff6b6b; font-weight: bold;")  # 红色表示错误
                self.a_detail_btn.setEnabled(False)
                QMessageBox.warning(self, "警告", "选择的文件夹中没有找到视频文件")

    def select_b_video_folder(self):
        """选择B视频文件夹或图片文件夹（根据功能而定）"""
        selected_function = self.function_combo.currentData()

        if selected_function in ["fire_ks", "fenghuo_v2_cpu", "fenghuo_v2_nvenc", "platypus_ks"]:
            # 星火KS、风火轮、风火轮二代和鸭嘴兽功能：选择图片文件夹
            folder_path = QFileDialog.getExistingDirectory(self, "选择图片文件夹")
            if folder_path:
                self.b_video_folder = folder_path
                self.b_folder_path = folder_path  # 保存路径供处理器使用

                # 统计图片文件
                image_count = self.count_image_files(folder_path)
                folder_name = os.path.basename(folder_path)

                if image_count > 0:
                    self.b_label.setText(f"图片文件夹: {folder_name} ({image_count} 个图片)")
                    self.b_label.setStyleSheet("color: #00ff00; font-weight: bold;")  # 亮绿色
                    self.b_detail_btn.setEnabled(False)  # 图片文件夹不显示详情
                else:
                    self.b_label.setText(f"图片文件夹: {folder_name} (无图片文件)")
                    self.b_label.setStyleSheet("color: #ff6b6b; font-weight: bold;")  # 红色表示错误
                    self.b_detail_btn.setEnabled(False)
                    QMessageBox.warning(self, "警告", "选择的文件夹中没有找到图片文件")
        else:
            # 其他功能：选择视频文件夹
            folder_path = QFileDialog.getExistingDirectory(self, "选择B视频文件夹（循环视频）")
            if folder_path:
                self.b_video_folder = folder_path
                self.b_folder_path = folder_path  # 保存路径供处理器使用
                self.b_video_list = self.get_video_files_from_folder(folder_path)

                folder_name = os.path.basename(folder_path)
                video_count = len(self.b_video_list)

                if video_count > 0:
                    self.b_label.setText(f"B视频文件夹: {folder_name} ({video_count} 个视频)")
                    self.b_label.setStyleSheet("color: #00ff00; font-weight: bold;")  # 亮绿色
                    self.b_detail_btn.setEnabled(True)
                else:
                    self.b_label.setText(f"B视频文件夹: {folder_name} (无视频文件)")
                    self.b_label.setStyleSheet("color: #ff6b6b; font-weight: bold;")  # 红色表示错误
                    self.b_detail_btn.setEnabled(False)
                    QMessageBox.warning(self, "警告", "选择的文件夹中没有找到视频文件")

    def count_image_files(self, folder_path):
        """统计文件夹中的图片文件数量"""
        if not folder_path or not os.path.exists(folder_path):
            return 0

        count = 0
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp']

        try:
            for filename in os.listdir(folder_path):
                file_path = os.path.join(folder_path, filename)
                if os.path.isfile(file_path):
                    _, ext = os.path.splitext(filename.lower())
                    if ext in image_extensions:
                        count += 1
        except Exception as e:
            print(f"统计图片文件失败: {e}")

        return count

    def show_a_video_list(self):
        """显示A视频列表详情"""
        if self.a_video_list:
            dialog = VideoListDialog(self, self.a_video_list, "A视频文件夹详情")
            if dialog.exec_() == QDialog.Accepted:
                # 获取重新排序后的视频列表
                self.a_video_list = dialog.get_video_list()
                # 更新A视频显示
                self.update_a_video_display()

    def show_b_video_list(self):
        """显示B视频列表详情"""
        if self.b_video_list:
            dialog = VideoListDialog(self, self.b_video_list, "B视频文件夹详情")
            if dialog.exec_() == QDialog.Accepted:
                # 获取重新排序后的视频列表
                self.b_video_list = dialog.get_video_list()
                # 更新B视频显示
                self.update_b_video_display()

    def update_a_video_display(self):
        """更新A视频文件夹显示"""
        if self.a_video_folder:
            folder_name = os.path.basename(self.a_video_folder)
            if self.a_video_list:
                video_count = len(self.a_video_list)
                self.a_label.setText(f"A视频文件夹: {folder_name} ({video_count} 个视频)")
                self.a_label.setStyleSheet("color: #00ff00; font-weight: bold;")  # 亮绿色
                self.a_detail_btn.setEnabled(True)
            else:
                # 文件夹存在但没有视频
                self.a_label.setText(f"A视频文件夹: {folder_name} (0 个视频)")
                self.a_label.setStyleSheet("color: #ff6b6b; font-weight: bold;")  # 红色表示错误
                self.a_detail_btn.setEnabled(False)
        else:
            # 没有选择文件夹
            self.a_label.setText("A视频文件夹 (主视频): 未选择")
            self.a_label.setStyleSheet("color: #888888;")  # 灰色
            self.a_detail_btn.setEnabled(False)

    def update_b_video_display(self):
        """更新B视频文件夹显示"""
        selected_function = self.function_combo.currentData()

        if self.b_video_folder or self.b_folder_path:
            folder_path = self.b_video_folder or self.b_folder_path
            folder_name = os.path.basename(folder_path)

            if selected_function in ["fire_ks", "fenghuo_v2_cpu", "fenghuo_v2_nvenc"]:
                # 星火KS、风火轮和风火轮二代功能显示图片数量
                if hasattr(self, 'b_image_count') and self.b_image_count > 0:
                    self.b_label.setText(f"图片文件夹: {folder_name} ({self.b_image_count} 张图片)")
                    self.b_label.setStyleSheet("color: #00ff00; font-weight: bold;")  # 亮绿色
                    self.b_detail_btn.setEnabled(False)  # 图片文件夹不需要详情按钮
                else:
                    self.b_label.setText(f"图片文件夹: {folder_name} (0 张图片)")
                    self.b_label.setStyleSheet("color: #ff6b6b; font-weight: bold;")  # 红色表示错误
                    self.b_detail_btn.setEnabled(False)
            else:
                # 其他功能显示视频数量
                if self.b_video_list:
                    video_count = len(self.b_video_list)
                    self.b_label.setText(f"B视频文件夹: {folder_name} ({video_count} 个视频)")
                    self.b_label.setStyleSheet("color: #00ff00; font-weight: bold;")  # 亮绿色
                    self.b_detail_btn.setEnabled(True)
                else:
                    self.b_label.setText(f"B视频文件夹: {folder_name} (0 个视频)")
                    self.b_label.setStyleSheet("color: #ff6b6b; font-weight: bold;")  # 红色表示错误
                    self.b_detail_btn.setEnabled(False)
        else:
            # 没有选择文件夹
            if selected_function in ["fire_ks"]:
                self.b_label.setText("图片文件夹: 未选择")
            else:
                self.b_label.setText("B视频文件夹 (循环视频): 未选择")
            self.b_label.setStyleSheet("color: #888888;")  # 灰色
            self.b_detail_btn.setEnabled(False)

    def select_a_video(self):
        """选择A视频"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择A视频 (主视频)", "", "视频文件 (*.mp4 *.avi *.mov *.mkv);;所有文件 (*)"
        )
        if file_path:
            self.a_video = file_path
            self.a_label.setText(f"A视频: {os.path.basename(file_path)}")

    def select_b_video(self):
        """选择B视频"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择B视频 (循环视频)", "", "视频文件 (*.mp4 *.avi *.mov *.mkv);;所有文件 (*)"
        )
        if file_path:
            self.b_video = file_path
            self.b_label.setText(f"B视频: {os.path.basename(file_path)}")

    def select_output_dir(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.output_dir = dir_path
            self.output_label.setText(f"输出: {os.path.basename(dir_path)}")

    def get_video_files_from_folder(self, folder_path):
        """从文件夹获取视频文件列表（优化版本）"""
        if not folder_path or not os.path.exists(folder_path):
            return []

        # 支持的视频格式
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.ts', '.mts', '.m2ts'}
        video_files = []

        try:
            print(f"正在扫描文件夹: {folder_path}")

            # 获取所有文件
            all_files = []
            for filename in os.listdir(folder_path):
                file_path = os.path.join(folder_path, filename)
                if os.path.isfile(file_path):
                    _, ext = os.path.splitext(filename.lower())
                    if ext in video_extensions:
                        all_files.append((file_path, filename))

            print(f"找到 {len(all_files)} 个视频文件")

            # 批量获取时长信息
            for file_path, filename in all_files:
                try:
                    duration = self.get_video_duration(file_path)
                    video_files.append({
                        'path': file_path,
                        'filename': filename,
                        'duration': duration
                    })
                except Exception as e:
                    print(f"处理文件失败 {filename}: {e}")
                    # 即使获取时长失败，也添加到列表中
                    video_files.append({
                        'path': file_path,
                        'filename': filename,
                        'duration': 0
                    })

            # 按文件名自然排序
            video_files.sort(key=lambda x: x['filename'].lower())
            print(f"视频文件列表处理完成，共 {len(video_files)} 个文件")

        except PermissionError:
            print(f"没有权限访问文件夹: {folder_path}")
        except Exception as e:
            print(f"获取视频文件列表失败: {e}")

        return video_files

    def get_video_duration(self, video_path):
        """获取视频时长（优化版本）"""
        if not os.path.exists(video_path):
            return 0

        try:
            ffprobe_path = self.ffmpeg_path.replace("ffmpeg.exe", "ffprobe.exe")
            if not os.path.exists(ffprobe_path):
                print(f"ffprobe不存在: {ffprobe_path}")
                return 0

            # 使用更快的ffprobe命令
            result = subprocess.run(
                [ffprobe_path, "-v", "quiet", "-select_streams", "v:0",
                 "-show_entries", "format=duration", "-of", "csv=p=0", video_path],
                stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True,
                creationflags=subprocess.CREATE_NO_WINDOW, timeout=5
            )

            if result.returncode == 0 and result.stdout.strip():
                duration_str = result.stdout.strip()
                if duration_str and duration_str != "N/A":
                    return float(duration_str)

            # 如果上面的方法失败，尝试备用方法
            result = subprocess.run(
                [ffprobe_path, "-v", "quiet", "-show_entries", "stream=duration",
                 "-select_streams", "v:0", "-of", "csv=p=0", video_path],
                stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True,
                creationflags=subprocess.CREATE_NO_WINDOW, timeout=5
            )

            if result.returncode == 0 and result.stdout.strip():
                duration_str = result.stdout.strip()
                if duration_str and duration_str != "N/A":
                    return float(duration_str)

            return 0

        except subprocess.TimeoutExpired:
            print(f"获取视频时长超时: {video_path}")
            return 0
        except (ValueError, TypeError) as e:
            print(f"解析时长数据失败: {e}")
            return 0
        except Exception as e:
            print(f"获取视频时长失败: {e}")
            return 0

    def open_chatroom(self):
        """打开聊天室窗口"""
        print(f"[主界面] 打开聊天室，传递卡密: {self.card_key}")

        if self.chatroom_window is None:
            self.chatroom_window = ChatRoom(self, self.card_key)

        # 定位到主窗口右侧
        self.chatroom_window.position_next_to_parent()
        self.chatroom_window.show()
        self.chatroom_window.raise_()
        self.chatroom_window.activateWindow()

    def init_function_options(self):
        """初始化功能选项"""
        # 定义各平台的功能选项
        self.platform_functions = {
            "jf_single_dy_platform": [
                ("疾风DY单A（大展宏图二代）⚡", "jf_single_dy")
            ],
            "dy_platform": [
                ("竹节草（DY）3.6新短剧专用通道🎋", "zhujiecao_dy"),
                ("罗莎（DY）3.6新短剧专用通道🌹（需卡8）", "luosha_dy"),
                ("野人先生 🦍顶级包过", "yeren_dy"),
                ("魔法少女 🪄短剧首选", "mofa_dy"),
                ("海绵宝宝 🧽短剧首选", "haimian_dy"),
                ("小吊梨汤", "dy")
            ],
            "ks_platform": [
                ("星火", "fire_ks"),
                ("风火轮二代 (CPU)", "fenghuo_v2_cpu"),
                ("风火轮二代 (N卡加速)", "fenghuo_v2_nvenc"),
                ("鸭嘴兽 💪24号最新稳定过框", "platypus_ks"),
                ("夏日狂欢 🌞爆弹一夏", "kuangsha_ks")
            ],
            "tk_platform": [
                ("浪漫满屋 💕3.0力推", "langman_manwu"),
                ("提tok", "mogu_ab")
            ],
            "sph_platform": [
                ("金刚狼 🦾稳过爆单", "wolverine_sph")
            ]
        }

        # 设置默认平台的功能选项（疾风单A）
        self.update_function_options("jf_single_dy_platform")

    def on_platform_changed(self, index):
        """平台选择改变时的处理"""
        platform_data = self.platform_combo.itemData(index)
        self.update_function_options(platform_data)

    def update_function_options(self, platform):
        """更新功能选项"""
        self.function_combo.clear()
        if platform in self.platform_functions:
            for display_name, value in self.platform_functions[platform]:
                self.function_combo.addItem(display_name, value)
        self.function_combo.setCurrentIndex(0)

    def on_function_changed(self, function_name):
        """功能选择改变时的处理"""
        # 获取当前选择的功能代码
        selected_function = self.function_combo.currentData()

        if selected_function == "mogu_ab":  # 提tok功能
            # 显示提tok的提示
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("提tok功能")
            msg_box.setText(
                "提tok 功能要求：\n\n"
                "• A视频和B视频数量必须一致\n"
                "• 每个A视频对应一个B视频进行处理\n"
                "• 输出文件名格式：MoGuAB_A视频文件名.mkv\n"
                "• 分辨率以A视频为准，自动适配\n\n"
                "请确保A、B视频文件夹中的视频数量相同！"
            )
            msg_box.setIcon(QMessageBox.Information)
            msg_box.setStandardButtons(QMessageBox.Ok)

            msg_box.setStyleSheet("""
                QMessageBox {
                    background: #1a1a2e;
                    color: white;
                    font-family: "Microsoft YaHei";
                }
                QMessageBox QLabel {
                    color: #4caf50;
                    font-size: 16px;
                    font-weight: bold;
                    padding: 10px;
                    line-height: 1.4;
                }
                QMessageBox QPushButton {
                    background: #388e3c;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 20px;
                    font-size: 14px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QMessageBox QPushButton:hover {
                    background: #66bb6a;
                }
            """)

            msg_box.exec_()
        elif function_name == "星火（KS）":
            # 显示星火KS的提示
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("星火KS功能")
            msg_box.setText(
                "星火KS 功能要求：\n\n"
                "• A视频文件夹：选择视频文件\n"
                "• B视频文件夹：选择图片文件夹\n"
                "• 图片循环匹配A视频进行处理\n"
                "• 输出文件名格式：fire_A视频文件名.mp4\n"
                "• 分辨率和时长以A视频为准\n\n"
                "请确保图片文件夹中有足够的图片文件！"
            )
        elif function_name == "小吊梨汤（DY/KS）":
            # 显示小吊梨汤功能说明
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("小吊梨汤功能（DY/KS）")
            msg_box.setText(
                "小吊梨汤功能说明：\n\n"
                "• 适用于DY和KS平台的视频处理\n"
                "• A视频和B视频数量必须一致\n"
                "• 每个A视频对应一个B视频进行处理\n"
                "• 输出文件名格式：DY_A视频文件名.mp4\n\n"
                "请确保A、B视频文件夹中的视频数量相同！"
            )
            msg_box.setIcon(QMessageBox.Information)
            msg_box.setStandardButtons(QMessageBox.Ok)

            msg_box.setStyleSheet("""
                QMessageBox {
                    background: #1a1a2e;
                    color: white;
                    font-family: "Microsoft YaHei";
                }
                QMessageBox QLabel {
                    color: #bb86fc;
                    font-size: 16px;
                    font-weight: bold;
                    padding: 10px;
                    line-height: 1.4;
                }
                QMessageBox QPushButton {
                    background: #bb86fc;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 20px;
                    font-size: 14px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QMessageBox QPushButton:hover {
                    background: #d1c4e9;
                }
            """)

            msg_box.exec_()
            
        
        print(f"[功能选择] 切换到: {function_name}")

        # 恢复开始按钮的正常状态（移除维护限制）
        self.start_btn.setEnabled(True)
        self.start_btn.setStyleSheet("")  # 使用默认样式

    def detect_nvidia_gpu(self):
        """检测是否为Nvidia显卡"""
        try:
            import subprocess

            # 使用wmic命令查询显卡信息
            try:
                result = subprocess.run(
                    ['wmic', 'path', 'win32_VideoController', 'get', 'name'],
                    capture_output=True, text=True, encoding='utf-8', errors='ignore',
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0,
                    timeout=10
                )

                if result.returncode == 0:
                    output = result.stdout
                    # 解析显卡名称
                    lines = output.strip().split('\n')
                    gpu_names = []
                    for line in lines[1:]:  # 跳过标题行
                        line = line.strip()
                        if line and line != 'Name':
                            gpu_names.append(line)

                    # 检查是否有Nvidia显卡
                    for gpu_name in gpu_names:
                        if 'nvidia' in gpu_name.lower() or 'geforce' in gpu_name.lower() or 'quadro' in gpu_name.lower() or 'tesla' in gpu_name.lower():
                            return {
                                'is_nvidia': True,
                                'name': gpu_name,
                                'all_gpus': gpu_names
                            }

                    # 如果没有找到Nvidia显卡，返回第一个显卡名称
                    if gpu_names:
                        return {
                            'is_nvidia': False,
                            'name': gpu_names[0],
                            'all_gpus': gpu_names
                        }

            except (subprocess.TimeoutExpired, FileNotFoundError):
                # wmic命令失败，尝试使用PowerShell
                pass

            # 备用方案：使用PowerShell查询
            try:
                result = subprocess.run([
                    'powershell', '-Command',
                    'Get-WmiObject -Class Win32_VideoController | Select-Object -ExpandProperty Name'
                ], capture_output=True, text=True, encoding='utf-8', errors='ignore',
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0,
                timeout=10)

                if result.returncode == 0:
                    output = result.stdout.strip()
                    gpu_names = [line.strip() for line in output.split('\n') if line.strip()]

                    # 检查是否有Nvidia显卡
                    for gpu_name in gpu_names:
                        if 'nvidia' in gpu_name.lower() or 'geforce' in gpu_name.lower() or 'quadro' in gpu_name.lower() or 'tesla' in gpu_name.lower():
                            return {
                                'is_nvidia': True,
                                'name': gpu_name,
                                'all_gpus': gpu_names
                            }

                    # 如果没有找到Nvidia显卡
                    if gpu_names:
                        return {
                            'is_nvidia': False,
                            'name': gpu_names[0],
                            'all_gpus': gpu_names
                        }

            except (subprocess.TimeoutExpired, FileNotFoundError):
                pass

            # 如果所有方法都失败，返回默认值
            return {
                'is_nvidia': False,
                'name': '未知显卡',
                'all_gpus': ['未知显卡']
            }

        except Exception as e:
            print(f"[显卡检测] 检测失败: {e}")
            return {
                'is_nvidia': False,
                'name': '检测失败',
                'all_gpus': ['检测失败']
            }

    def update_b_folder_ui(self):
        """根据选择的功能更新B文件夹UI"""
        selected_function = self.function_combo.currentData()
        # 检查是否处于单视频模式
        is_single_mode = self.single_video_checkbox.isChecked()

        # 如果是疾风单A功能，隐藏B文件夹相关UI（只需要A视频）
        if selected_function == "jf_single_dy":
            self.b_label.setVisible(False)
            self.b_btn.setVisible(False)
            self.b_detail_btn.setVisible(False)
            self.single_video_btn.setVisible(False)
            self.prompt_display_label.setVisible(False)
            self.delete_used_b_checkbox.setVisible(False)
            return

        # 如果是单视频模式，隐藏B文件夹相关UI
        if is_single_mode:
            self.b_label.setVisible(False)
            self.b_btn.setVisible(False)
            self.b_detail_btn.setVisible(False)
            self.single_video_btn.setVisible(True)
            self.prompt_display_label.setVisible(True)
            self.prompt_display_label.setText(f"推流词: {self.selected_prompt}")
            return

        # 重置B文件夹相关状态
        self.b_video_folder = ""
        self.b_folder_path = ""
        self.b_video_list = []

        if selected_function in ["fire_ks", "fenghuo_v2_cpu", "fenghuo_v2_nvenc", "platypus_ks"]:
            # 星火KS、风火轮、风火轮二代和鸭嘴兽功能：显示为图片文件夹，显示删除B素材选项
            self.b_label.setText("图片文件夹: 未选择")
            self.b_label.setStyleSheet("color: #888888;")  # 未选择时为灰色
            self.b_btn.setText("选择图片文件夹")
            self.delete_used_b_checkbox.setVisible(True)
        else:
            # 其他功能：显示为B视频文件夹，显示删除B素材选项
            self.b_label.setText("B视频文件夹 (循环视频): 未选择")
            self.b_label.setStyleSheet("color: #888888;")  # 未选择时为灰色
            self.b_btn.setText("选择B视频文件夹")
            self.delete_used_b_checkbox.setVisible(True)

        # 恢复常规UI元素的可见性
        self.b_label.setVisible(True)
        self.b_btn.setVisible(True)
        self.b_detail_btn.setVisible(True)
        self.single_video_btn.setVisible(False)
        self.prompt_display_label.setVisible(False)

        # 禁用详情按钮
        self.b_detail_btn.setEnabled(False)

    def on_single_video_mode_changed(self, state):
        """单视频模式状态变化处理"""
        is_single_mode = state == Qt.Checked

        if is_single_mode:
            # 切换到单视频模式 - 隐藏整个B文件夹选择区域
            self.b_label.setVisible(False)
            self.b_btn.setVisible(False)
            self.b_detail_btn.setVisible(False)
            self.single_video_btn.setVisible(True)
            self.prompt_display_label.setVisible(True)
            self.prompt_display_label.setText(f"推流词: {self.selected_prompt}")
        else:
            # 切换回普通模式 - 显示B文件夹选择区域
            self.b_label.setVisible(True)
            self.b_btn.setVisible(True)
            self.b_detail_btn.setVisible(True)
            self.single_video_btn.setVisible(False)
            self.prompt_display_label.setVisible(False)

            # 恢复B文件夹UI文本
            selected_function = self.function_combo.currentData()
            if selected_function in ["fire_ks", "fenghuo_v2_cpu", "fenghuo_v2_nvenc", "platypus_ks"]:
                self.b_label.setText("图片文件夹: 未选择")
                self.b_btn.setText("选择图片文件夹")
            else:
                self.b_label.setText("B视频文件夹 (循环视频): 未选择")
                self.b_btn.setText("选择B视频文件夹")

            self.b_label.setStyleSheet("color: #888888;")  # 灰色

    def show_single_video_settings(self):
        """显示单视频推流设置对话框"""
        dialog = SingleVideoSettingsDialog(self, self.selected_prompt, self.custom_prompt)
        if dialog.exec_() == QDialog.Accepted:
            self.selected_prompt = dialog.selected_prompt
            self.custom_prompt = dialog.custom_prompt

            # 更新显示
            display_text = self.selected_prompt if self.selected_prompt != "随机" else "随机"
            self.prompt_display_label.setText(f"推流词: {display_text}")

            print(f"[单视频模式] 选择的提示词: {self.selected_prompt}")

        # 初始化单视频模式相关变量
        self.selected_prompt = "随机"  # 默认选择随机
        self.custom_prompt = ""  # 自定义提示词

    def start_processing(self):
        """开始处理"""
        # 首先检查是否到期
        self.check_expiry()

        # 检查输入
        if not self.a_video_folder or not self.a_video_list:
            QMessageBox.warning(self, "错误", "请先选择A视频文件夹且确保包含视频文件")
            return

        if not self.output_dir:
            QMessageBox.warning(self, "错误", "请先选择输出目录")
            return

        # 检查是否为单视频模式
        is_single_mode = self.single_video_checkbox.isChecked()
        selected_function = self.function_combo.currentData()

        # 如果是疾风单A功能，直接开始处理（不需要B素材）
        if selected_function == "jf_single_dy":
            self.start_jf_single_dy_processing()
            return

        # 如果是单视频模式，走单视频模式流程
        if is_single_mode:
            # 调用单视频模式处理流程
            self.start_single_video_processing()
            return

        # 非单视频模式的处理逻辑保持不变
        # 根据功能类型检查B文件夹
        if selected_function in ["fire_ks", "fenghuo_v2_cpu", "fenghuo_v2_nvenc", "platypus_ks"]:
            # 星火KS、风火轮、风火轮二代和鸭嘴兽功能：检查图片文件夹
            if not self.b_video_folder:
                QMessageBox.warning(self, "错误", "请先选择图片文件夹")
                return

            # 检查图片文件夹是否包含图片
            image_count = self.count_image_files(self.b_video_folder)
            if image_count == 0:
                QMessageBox.warning(self, "错误", "选择的图片文件夹中没有找到图片文件")
                return
        else:
            # 其他功能：检查B视频文件夹
            if not self.b_video_folder or not self.b_video_list:
                QMessageBox.warning(self, "错误", "请先选择B视频文件夹且确保包含视频文件")
                return

        # 检查特殊功能的要求
        if selected_function in ["mofa_dy", "haimian_dy", "mogu_ab", "dy"]:
            if len(self.a_video_list) != len(self.b_video_list):
                if selected_function == "mofa_dy":
                    function_name = "魔法少女（DY）"
                elif selected_function == "haimian_dy":
                    function_name = "海绵宝宝（DY）"
                elif selected_function == "langman_manwu":
                    function_name = "浪漫满屋（TK）"
                elif selected_function == "mogu_ab":
                    function_name = "提tok"
                elif selected_function == "dy":
                    function_name = "小吊梨汤（DY）"
                else:
                    function_name = "未知功能"
                msg_box = QMessageBox(self)
                msg_box.setWindowTitle(f"{function_name}功能要求")
                msg_box.setText(
                    f"{function_name}功能要求A视频和B视频数量必须一致！\n\n"
                    f"当前状态：\n"
                    f"A视频数量：{len(self.a_video_list)} 个\n"
                    f"B视频数量：{len(self.b_video_list)} 个\n\n"
                    f"请调整视频文件夹，确保A、B视频数量相同。"
                )
                msg_box.setIcon(QMessageBox.Warning)
                msg_box.setStandardButtons(QMessageBox.Ok)

                msg_box.setStyleSheet("""
                    QMessageBox {
                        background: #1a1a2e;
                        color: white;
                        font-family: "Microsoft YaHei";
                    }
                    QMessageBox QLabel {
                        color: #ffd700;
                        font-size: 16px;
                        font-weight: bold;
                        padding: 10px;
                        line-height: 1.4;
                    }
                    QMessageBox QPushButton {
                        background: #ff9800;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 8px 20px;
                        font-size: 14px;
                        font-weight: bold;
                        min-width: 80px;
                    }
                    QMessageBox QPushButton:hover {
                        background: #ffb74d;
                    }
                """)

                msg_box.exec_()
                return

        # 夏日狂欢和金刚狼功能不需要验证，直接处理
        if selected_function == "kuangsha_ks":
            self.start_kuangsha_processing()
        elif selected_function == "wolverine_sph":
            self.start_wolverine_processing()
        else:
            # 其他功能需要验证权限
            self.verify_feature_permission()

    def verify_feature_permission(self):
        """验证功能权限"""
        try:
            self.status_label.setText("正在验证功能权限...")

            # 根据选择的功能确定标识
            selected_function = self.function_combo.currentData()
            if selected_function == "jf_single_dy":
                feature_code = "07300001"  # 疾风单A功能标识
            elif selected_function == "yeren_dy":
                feature_code = "20250725"  # 野人先生功能标识
            elif selected_function == "mofa_dy":
                feature_code = "07080002"  # 魔法少女功能标识
            elif selected_function == "haimian_dy":
                feature_code = "07080003"  # 海绵宝宝功能标识
            elif selected_function == "langman_manwu":
                feature_code = "07220001"  # 浪漫满屋功能标识
            elif selected_function == "mogu_ab":
                feature_code = "moguab"  # 提tok功能标识
            elif selected_function == "fire_ks":
                feature_code = "MoneyKS"  # 星火KS功能标识
            elif selected_function == "dy":
                feature_code = "20250630"  # 小吊梨汤功能标识
            elif selected_function == "fenghuo_v2_cpu":
                feature_code = "07150001"  # 风火轮二代CPU功能标识
            elif selected_function == "fenghuo_v2_nvenc":
                feature_code = "07150002"  # 风火轮二代N卡功能标识
            elif selected_function == "platypus_ks":
                feature_code = "20250724"  # 鸭嘴兽功能标识
            elif selected_function == "wolverine_sph":
                feature_code = "wolverine_sph"  # 金刚狼功能标识（无需服务器验证）
            elif selected_function == "zhujiecao_dy":
                feature_code = "07310001"  # 竹节草功能标识
            elif selected_function == "luosha_dy":
                feature_code = "07310002"  # 罗莎功能标识
            else:
                feature_code = "20250725"  # 默认野人先生功能标识

            # 准备请求数据
            api_url = "http://47.122.30.31/api/verify.php"
            data = {
                "key": self.card_key,
                "feature_code": feature_code
            }

            headers = {
                "Content-Type": "application/json;charset=UTF-8"
            }

            # 发送验证请求
            print(f"[验证] 发送请求到: {api_url}")
            print(f"[验证] 请求数据: {data}")
            response = requests.post(api_url, json=data, headers=headers, timeout=10)
            response_data = response.json()
            print(f"[验证] 响应数据: {response_data}")

            if response_data.get("status") == True:
                # 获取加密的配置代码
                encrypted_code = response_data.get("data", {}).get("code", "")
                print(f"验证代码(加密): {encrypted_code}")

                # 解密代码
                if encrypted_code and self.aes_key and self.aes_iv:
                    try:
                        aes_key = self.aes_key.encode('utf-8')
                        aes_iv = self.aes_iv.encode('utf-8')

                        cipher = AES.new(aes_key, AES.MODE_CBC, aes_iv)
                        decrypted = unpad(cipher.decrypt(base64.b64decode(encrypted_code)), AES.block_size)
                        self.decrypted_code = decrypted.decode('utf-8').strip()
                        print(f"验证代码(解密): {self.decrypted_code}")

                        # 继续处理
                        self.continue_processing()

                    except Exception as e:
                        print(f"解密验证代码失败: {str(e)}")
                        QMessageBox.critical(self, '验证失败', '验证失败：请尝试重新启动或联系客服处理。')
                        self.status_label.setText("验证失败")
                else:
                    QMessageBox.critical(self, '验证失败', '验证失败：请尝试重新启动或联系客服处理。')
                    self.status_label.setText("验证失败")
            else:
                error_msg = response_data.get("message", "验证失败")
                QMessageBox.critical(self, '验证失败', '验证失败：请尝试重新启动或联系客服处理。')
                self.status_label.setText("验证失败")

        except Exception as e:
            print(f"[验证] 验证功能权限失败: {e}")
            import traceback
            print(f"[验证] 详细错误信息: {traceback.format_exc()}")
            QMessageBox.critical(self, '验证失败', '验证失败：请尝试重新启动或联系客服处理。')
            self.status_label.setText("验证失败")

    def continue_processing(self):
        """继续处理（验证成功后）"""
        print(f"[处理] 开始继续处理，解密代码: {self.decrypted_code}")
        if not self.decrypted_code:
            print("[处理] 解密代码为空，处理失败")
            QMessageBox.critical(self, "验证失败", "验证失败：请尝试重新启动或联系客服处理。")
            return

        # 设置UI状态
        self.is_processing = True
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("正在处理...")

        # 获取线程数设置
        thread_count = self.thread_spinbox.value()

        # 获取删除B素材的选项
        delete_used_b = self.delete_used_b_checkbox.isChecked()

        # 根据选择的功能创建不同的处理器
        selected_function = self.function_combo.currentData()
        print(f"[处理] 选择的功能: {selected_function}")
        print(f"[处理] 删除已用B素材: {delete_used_b}")

        if selected_function == "yeren_dy":
            # 导入野人先生处理器
            print("[处理] 创建野人先生处理器")
            from Codebase_Yeren import YerenProcessor
            self.worker = YerenProcessor(
                self.ffmpeg_path, self.a_video_list, self.b_video_list,
                self.output_dir, self.decrypted_code, delete_used_b
            )
        elif selected_function == "mofa_dy":
            # 导入魔法少女处理器
            print("[处理] 创建魔法少女处理器")
            from Codebase_mofa import MofaProcessor
            self.worker = MofaProcessor(
                self.ffmpeg_path, self.a_video_list, self.b_video_list,
                self.output_dir, self.decrypted_code, delete_used_b
            )
        elif selected_function == "haimian_dy":
            # 导入海绵宝宝处理器
            print("[处理] 创建海绵宝宝处理器")
            from Codebase_haimian import HaimianProcessor
            self.worker = HaimianProcessor(
                self.ffmpeg_path, self.a_video_list, self.b_video_list,
                self.output_dir, self.decrypted_code, delete_used_b
            )
        elif selected_function == "langman_manwu":
            # 导入浪漫满屋处理器
            print("[处理] 创建浪漫满屋处理器")
            from Codebase_LangmanManwu import LangmanManwuProcessor
            self.worker = LangmanManwuProcessor(
                self.ffmpeg_path, self.a_video_list, self.b_video_list,
                self.output_dir, self.decrypted_code, delete_used_b
            )
        elif selected_function == "mogu_ab":
            # 导入提tok处理器
            print("[处理] 创建提tok处理器")
            from Codebase_mogu import MoGuABProcessor
            self.worker = MoGuABProcessor(
                self.ffmpeg_path, self.a_video_list, self.b_video_list,
                self.output_dir, self.decrypted_code, delete_used_b
            )
        elif selected_function == "fire_ks":
            # 导入星火KS处理器
            print("[处理] 创建星火KS处理器")
            from MoneyKS import FireProcessor
            # 星火KS功能使用图片文件夹而不是B视频列表
            image_folder_path = self.b_video_folder  # B文件夹路径作为图片文件夹
            self.worker = FireProcessor(
                self.ffmpeg_path, self.a_video_list, image_folder_path,
                self.output_dir, self.decrypted_code, delete_used_b
            )
        elif selected_function == "fenghuo_v2_cpu":
            # 导入风火轮二代CPU处理器
            print("[处理] 创建风火轮二代CPU处理器")
            # 风火轮二代CPU功能使用图片文件夹而不是B视频列表
            image_folder_path = self.b_video_folder  # B文件夹路径作为图片文件夹
            self.worker = FenghuoV2Processor(
                self.ffmpeg_path, self.a_video_list, image_folder_path,
                self.output_dir, self.decrypted_code, delete_used_b
            )
        elif selected_function == "fenghuo_v2_nvenc":
            # 导入风火轮二代N卡处理器
            print("[处理] 创建风火轮二代N卡处理器")
            # 风火轮二代N卡功能使用图片文件夹而不是B视频列表
            image_folder_path = self.b_video_folder  # B文件夹路径作为图片文件夹
            self.worker = FenghuoV2NvencProcessor(
                self.ffmpeg_path, self.a_video_list, image_folder_path,
                self.output_dir, self.decrypted_code, delete_used_b
            )
        elif selected_function == "platypus_ks":
            # 导入鸭嘴兽处理器
            print("[处理] 创建鸭嘴兽处理器")
            # 鸭嘴兽功能使用图片文件夹而不是B视频列表
            image_folder_path = self.b_video_folder  # B文件夹路径作为图片文件夹
            self.worker = PlatypusProcessor(
                self.ffmpeg_path, self.a_video_list, image_folder_path,
                self.output_dir, self.decrypted_code, delete_used_b
            )
        elif selected_function == "luosha_dy":
            # 导入罗莎处理器
            print("[处理] 创建罗莎处理器")
            from Codebase_LuoSha import LuoShaProcessor
            self.worker = LuoShaProcessor(
                self.ffmpeg_path, self.a_video_list, self.b_video_list,
                self.output_dir, self.decrypted_code, delete_used_b
            )
        elif selected_function == "zhujiecao_dy":
            # 导入竹节草处理器
            print("[处理] 创建竹节草处理器")
            from Codebase_ZhuJieCao import ZhuJieCaoProcessor
            self.worker = ZhuJieCaoProcessor(
                self.ffmpeg_path, self.a_video_list, self.b_video_list,
                self.output_dir, self.decrypted_code, delete_used_b
            )
        elif selected_function == "dy":
            # 导入小吊梨汤处理器
            print("[处理] 创建小吊梨汤处理器")
            from Codebase_DY import DYProcessor
            self.worker = DYProcessor(
                self.ffmpeg_path, self.a_video_list, self.b_video_list,
                self.output_dir, self.decrypted_code, delete_used_b
            )
        elif selected_function == "jf_single_dy":
            # 导入疾风单A处理器
            print("[处理] 创建疾风单A处理器")
            from Codebase_JFSingleDY import JFSingleDYProcessor
            self.worker = JFSingleDYProcessor(
                self.ffmpeg_path, self.a_video_list,
                self.output_dir, self.decrypted_code
            )
        elif selected_function == "wolverine_sph":
            # 导入金刚狼处理器
            print("[处理] 创建金刚狼处理器")
            self.worker = WolverineProcessor(
                self.ffmpeg_path, self.a_video_list, self.b_video_list,
                self.output_dir, self.decrypted_code, delete_used_b
            )
        else:
            # 默认使用野人先生处理器
            print("[处理] 创建野人先生处理器")
            from Codebase_Yeren import YerenProcessor
            self.worker = YerenProcessor(
                self.ffmpeg_path, self.a_video_list, self.b_video_list,
                self.output_dir, self.decrypted_code, delete_used_b
            )

        # 连接信号
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.process_finished.connect(self.on_process_finished)

        # 启动处理
        self.worker.start()

    def stop_processing(self):
        """停止处理"""
        if self.worker:
            self.worker.cancel()
            self.worker.wait()

        self.is_processing = False
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.status_label.setText("处理已停止")

    def update_progress(self, value, message):
        """更新进度"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)

    def on_process_finished(self, success, message):
        """处理完成"""
        self.is_processing = False
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)

        if success:
            self.progress_bar.setValue(100)
            self.status_label.setText("处理完成")
            QMessageBox.information(self, "完成", message)
        else:
            self.status_label.setText("处理失败")
            QMessageBox.critical(self, "错误", message)

    def setup_duration_patch_ui(self):
        """设置八秒处理UI"""
        # 创建八秒处理区域
        duration_group = QGroupBox("改时长-每3小时最新参数云处理-自测效果不是必须要处理")
        duration_group.setObjectName("duration_group")
        duration_layout = QVBoxLayout()

        # 时长设置
        duration_control_layout = QHBoxLayout()
        duration_control_layout.addWidget(QLabel("目标时长(秒):"))

        self.duration_spinbox = QSpinBox()
        self.duration_spinbox.setRange(1, 3600)
        self.duration_spinbox.setValue(8)  # 默认8秒
        self.duration_spinbox.setObjectName("duration_spinbox")
        duration_control_layout.addWidget(self.duration_spinbox)

        duration_control_layout.addStretch()
        duration_layout.addLayout(duration_control_layout)

        # 选择文件夹按钮
        self.duration_folder_btn = QPushButton("选择文件夹批量处理")
        self.duration_folder_btn.setObjectName("duration_folder_btn")
        self.duration_folder_btn.clicked.connect(self.start_duration_patch)
        duration_layout.addWidget(self.duration_folder_btn)

        duration_group.setLayout(duration_layout)
        return duration_group

    def start_duration_patch(self):
        """开始八秒处理"""
        try:
            # 禁用按钮防止重复点击
            self.duration_folder_btn.setEnabled(False)
            self.duration_spinbox.setEnabled(False)
            self.status_label.setText("正在连接服务器取最新参数...")

            # 构建验证请求数据（参考MoneyComehere.py的处理方法）
            api_url = "http://47.122.30.31/api/verify.php"
            data = {
                "key": self.card_key,
                "feature_code": "eight"  # 八秒处理功能标识
            }
            headers = {"Content-Type": "application/json;charset=UTF-8"}

            self.patch_verify_thread = QThread()
            self.patch_verify_worker = NetworkRequestWorker(api_url, data, headers)
            self.patch_verify_worker.moveToThread(self.patch_verify_thread)

            self.patch_verify_thread.started.connect(self.patch_verify_worker.run)
            self.patch_verify_worker.finished.connect(self.handle_patch_duration_response)
            self.patch_verify_worker.error.connect(self.handle_patch_duration_error)

            self.patch_verify_worker.finished.connect(self.patch_verify_thread.quit)
            self.patch_verify_worker.finished.connect(self.patch_verify_worker.deleteLater)
            self.patch_verify_thread.finished.connect(self.patch_verify_thread.deleteLater)

            self.patch_verify_thread.start()

        except Exception as e:
            self.handle_patch_duration_error(f"启动验证失败: {e}")

    def handle_patch_duration_response(self, response_data):
        """处理八秒处理验证响应"""
        try:
            if response_data.get("status") == True:
                # 获取关键代码
                encrypted_code = response_data.get("data", {}).get("code", "")
                print(f"八秒处理验证代码(加密): {encrypted_code}")

                # 解密代码
                decrypted_code = None
                if encrypted_code and hasattr(self, 'aes_key') and hasattr(self, 'aes_iv'):
                    try:
                        # 使用从登录时获取的 AES key 和 iv
                        aes_key = self.aes_key.encode('utf-8')
                        aes_iv = self.aes_iv.encode('utf-8')

                        cipher = AES.new(aes_key, AES.MODE_CBC, aes_iv)
                        decrypted = unpad(cipher.decrypt(base64.b64decode(encrypted_code)), AES.block_size)
                        decrypted_code = decrypted.decode('utf-8')
                        print(f"八秒处理验证代码(解密): {decrypted_code}")

                        # 检查是否有警告信息
                        if "warning" in response_data.get("data", {}):
                            print(f"警告: {response_data['data']['warning']}")

                    except Exception as e:
                        print(f"解密八秒处理验证代码失败: {str(e)}")
                        decrypted_code = None

                if decrypted_code:
                    # 验证成功，继续选择文件夹并处理
                    self.continue_patch_duration(decrypted_code)
                else:
                    self.handle_patch_duration_error("未能获取有效的配置参数")
            else:
                # 验证失败
                self.handle_patch_duration_error("验证失败，请联系客服")

        except Exception as e:
            self.handle_patch_duration_error(f"处理验证响应失败: {e}")

    def handle_patch_duration_error(self, error_msg):
        """处理八秒处理验证错误"""
        QMessageBox.warning(self, "验证失败", "验证失败，请尝试重新登录或联系客服处理")
        self.status_label.setText("验证失败")
        self.status_label.setStyleSheet("color: #e74c3c;")
        self.duration_folder_btn.setEnabled(True)
        self.duration_spinbox.setEnabled(True)

    def continue_patch_duration(self, patch_config):
        """验证成功后，继续八秒处理的流程"""
        folder = QFileDialog.getExistingDirectory(self, "选择视频文件夹")
        if not folder:
            self.duration_folder_btn.setEnabled(True)
            self.duration_spinbox.setEnabled(True)
            self.status_label.setText("操作已取消")
            return

        files = []
        for filename in os.listdir(folder):
            file_path = os.path.join(folder, filename)
            if self.is_video_file(file_path):
                files.append(file_path)

        if not files:
            QMessageBox.information(self, "提示", "该文件夹下没有可处理的视频文件。")
            self.duration_folder_btn.setEnabled(True)
            self.duration_spinbox.setEnabled(True)
            return

        duration = self.duration_spinbox.value()
        self.status_label.setText("正在批量修改视频时长...")

        # 创建输出目录
        output_dir = os.path.join(folder, f"{duration}s处理输出")
        os.makedirs(output_dir, exist_ok=True)

        # 启动处理线程
        self.duration_patch_thread = DurationPatchThread(files, duration, patch_config, output_dir)
        self.duration_patch_thread.finished.connect(lambda: self.status_label.setText("批量时长修改完成"))
        self.duration_patch_thread.finished.connect(lambda: self.duration_folder_btn.setEnabled(True))
        self.duration_patch_thread.finished.connect(lambda: self.duration_spinbox.setEnabled(True))
        self.duration_patch_thread.start()

    def is_video_file(self, file_path):
        """检查是否为视频文件"""
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.m4v', '.3gp', '.webm']
        return any(file_path.lower().endswith(ext) for ext in video_extensions)

    def start_single_video_processing(self):
        """单视频模式处理流程"""
        # 设置UI状态
        self.is_processing = True
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("正在准备单视频模式处理...")
        
        # 判断是否为图片模式功能（星火KS、风火轮和风火轮二代需要图片而非视频）
        selected_function = self.function_combo.currentData()
        is_image_mode = (selected_function in ["fire_ks", "fenghuo_v2_cpu", "fenghuo_v2_nvenc"])
        
        # 创建并启动下载工作线程
        self.download_worker = VideoApiDownloader(
            self.a_video_list, 
            self.output_dir, 
            self.selected_prompt,
            is_image_mode
        )
        
        # 连接信号
        self.download_worker.progress_updated.connect(self.update_progress)
        self.download_worker.download_finished.connect(self.on_download_finished)
        
        # 启动下载
        self.download_worker.start()
        
        # 启用停止按钮
        self.stop_btn.setEnabled(True)
        
    def on_download_finished(self, success, message, temp_dir):
        """下载完成后的处理"""
        if not success:
            # 下载失败
            self.is_processing = False
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            self.status_label.setText("服务器回传处理视频失败！请检查网络环境重试。")
            
            # 检查是否是抓包工具检测的错误信息
            if "D加密出现验证问题" in message:
                # 显示加密验证问题警告并退出程序
                QMessageBox.critical(self, "安全警告", message)
                print("检测到抓包工具，程序将退出")
                QApplication.quit()  # 直接退出程序
                return
                
            QMessageBox.critical(self, "错误", message)
            return
            
        # 下载成功，设置临时目录为B素材文件夹
        self.b_video_folder = temp_dir
        self.b_folder_path = temp_dir
        
        # 获取B素材列表
        selected_function = self.function_combo.currentData()
        if selected_function in ["fire_ks", "fenghuo_v2_cpu", "fenghuo_v2_nvenc"]:
            # 星火KS、风火轮和风火轮二代功能使用图片
            self.b_image_count = len([f for f in os.listdir(temp_dir) if os.path.isfile(os.path.join(temp_dir, f))])
            self.b_label.setText(f"图片文件夹: 临时目录 ({self.b_image_count} 张图片)")
        else:
            # 其他功能使用视频
            self.b_video_list = self.get_video_files_from_folder(temp_dir)
            self.b_label.setText(f"B视频文件夹: 临时目录 ({len(self.b_video_list)} 个视频)")
            
        self.b_label.setStyleSheet("color: #00ff00; font-weight: bold;")  # 亮绿色
        
        # 验证功能权限，继续处理流程
        self.verify_feature_permission()

    def start_jf_single_dy_processing(self):
        """疾风单A处理流程"""
        # 设置UI状态
        self.is_processing = True
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("正在准备疾风单A处理...")

        # 验证功能权限
        self.verify_feature_permission()

    def stop_processing(self):
        """停止处理"""
        try:
            if hasattr(self, 'download_worker') and self.download_worker and self.download_worker.isRunning():
                print("正在取消下载任务...")
                self.download_worker.cancel()
                self.download_worker.wait()
                print("下载任务已取消")
                
            if hasattr(self, 'worker') and self.worker:
                print("正在取消处理任务...")
                self.worker.cancel()
                self.worker.wait()
                print("处理任务已取消")

            # 如果是单视频模式，清理临时目录
            is_single_mode = self.single_video_checkbox.isChecked()
            if is_single_mode and hasattr(self, 'b_folder_path') and self.b_folder_path and os.path.exists(self.b_folder_path):
                try:
                    folder_name = os.path.basename(self.b_folder_path)
                    if folder_name.startswith(".cache_") or folder_name.startswith("temp_api_"):
                        print(f"停止处理时清理临时目录: {self.b_folder_path}")
                        self.clean_temp_dir(self.b_folder_path)
                except Exception as e:
                    print(f"停止处理时清理临时目录失败: {e}")

            self.is_processing = False
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            self.status_label.setText("处理已停止")
        except Exception as e:
            print(f"停止处理失败: {e}")
            self.status_label.setText("停止处理失败")
        
    def on_process_finished(self, success, message):
        """处理完成"""
        try:
            self.is_processing = False
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)

                    # 如果是单视频模式，处理完成后清理临时目录
            is_single_mode = self.single_video_checkbox.isChecked()
            if is_single_mode and hasattr(self, 'b_folder_path') and self.b_folder_path and os.path.exists(self.b_folder_path):
                try:
                    # 检查是否为临时目录路径（以.cache_开头或temp_api_开头）
                    folder_name = os.path.basename(self.b_folder_path)
                    if folder_name.startswith(".cache_") or folder_name.startswith("temp_api_"):
                        print(f"准备清理临时目录: {self.b_folder_path}")
                        # 延迟1秒再删除，确保文件不再被使用
                        QTimer.singleShot(1000, lambda: self.clean_temp_dir(self.b_folder_path))
                except Exception as e:
                    print(f"准备清理临时目录失败: {e}")

            if success:
                    self.progress_bar.setValue(100)
                    self.status_label.setText("处理完成")
                    QMessageBox.information(self, "完成", message)
            else:
                    self.status_label.setText("处理失败")
                    QMessageBox.critical(self, "错误", message)
        except Exception as e:
            print(f"处理完成回调出错: {e}")
            
    def clean_temp_dir(self, dir_path):
        """清理临时目录（延迟调用）"""
        try:
            if os.path.exists(dir_path):
                shutil.rmtree(dir_path)
                print(f"已清理临时目录: {dir_path}")
        except Exception as e:
            print(f"清理临时目录失败: {e}")

    def start_kuangsha_processing(self):
        """启动夏日狂欢处理（无需验证）"""
        print("[夏日狂欢] 开始处理，无需服务器验证")

        # 设置UI状态
        self.is_processing = True
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("正在准备夏日狂欢处理...")

        # 获取线程数设置
        thread_count = self.thread_spinbox.value()

        # 获取删除B素材的选项
        delete_used_b = self.delete_used_b_checkbox.isChecked()

        # 获取A视频路径列表和B视频路径列表
        a_video_paths = [video_info['path'] for video_info in self.a_video_list]
        b_video_paths = [video_info['path'] for video_info in self.b_video_list]

        # 创建夏日狂欢处理器
        print("[处理] 创建夏日狂欢处理器")
        self.worker = KuangShaProcessor(
            self.ffmpeg_path, a_video_paths, b_video_paths,
            self.output_dir, "夏日狂欢", delete_used_b
        )

        # 连接信号
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.process_finished.connect(self.on_process_finished)

        # 启动处理
        self.worker.start()

    def start_wolverine_processing(self):
        """启动金刚狼处理（无需验证）"""
        print("[金刚狼] 开始处理，无需服务器验证")

        # 设置UI状态
        self.is_processing = True
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("正在准备金刚狼处理...")

        # 获取删除B素材的选项
        delete_used_b = self.delete_used_b_checkbox.isChecked()

        # 获取A视频路径列表和B视频路径列表
        a_video_paths = [video_info['path'] for video_info in self.a_video_list]
        b_video_paths = [video_info['path'] for video_info in self.b_video_list]

        # 创建金刚狼处理器
        print("[处理] 创建金刚狼处理器")
        self.worker = WolverineProcessor(
            self.ffmpeg_path, a_video_paths, b_video_paths,
            self.output_dir, "金刚狼", delete_used_b
        )

        # 连接信号
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.process_finished.connect(self.on_process_finished)

        # 启动处理
        self.worker.start()


class DurationPatchThread(QThread):
    """八秒处理线程"""
    def __init__(self, files, duration, patch_config, output_dir, parent=None):
        super().__init__(parent)
        self.files = files
        self.duration = duration
        self.patch_config = patch_config
        self.output_dir = output_dir

    def run(self):
        """执行八秒处理"""
        for file in self.files:
            out_file = os.path.join(self.output_dir, f"{self.duration}s处理_{os.path.basename(file)}")
            try:
                self.patch_mp4_duration(file, out_file, self.duration)
                print(f"八秒处理完成: {out_file}")
            except Exception as e:
                print(f"八秒处理失败: {file} -> {e}")

    def patch_mp4_duration(self, input_path, output_path, seconds):
        """修改MP4文件时长"""
        try:
            # 读取文件数据
            with open(input_path, 'rb') as f:
                data = bytearray(f.read())

            # 调用修改时长的函数
            self.patch_duration_data(data, seconds)

            # 写入输出文件
            with open(output_path, 'wb') as f:
                f.write(data)

        except Exception as e:
            raise Exception(f"修改时长失败: {e}")

    def patch_duration_data(self, buf, seconds):
        """修改视频数据中的时长信息"""
        # mvhd
        mv = buf.find(b'mvhd')
        if mv != -1:
            ver = buf[mv + 4]
            mv_dur_off = mv + 4 + 1 + 3 + (4 if ver == 0 else 8) + (4 if ver == 0 else 8) + 4
            ts = self.mvhd_timescale(buf)
            self.write_duration(buf, mv_dur_off, ver, int(round(seconds * ts)))

        # tkhd / mdhd
        for atom in (b'tkhd', b'mdhd'):
            pos = 0
            while True:
                idx = buf.find(atom, pos)
                if idx == -1:
                    break
                ver = buf[idx + 4]
                if atom == b'tkhd':
                    base = idx + 4 + 1 + 3 + (4 if ver == 0 else 8) + (4 if ver == 0 else 8) + 8
                    ts = self.mvhd_timescale(buf)
                else:  # mdhd
                    ts_off = idx + 4 + 1 + 3 + (4 if ver == 0 else 8) + (4 if ver == 0 else 8)
                    ts_local = self.read_u32(buf, ts_off)
                    base = ts_off + 4
                    self.write_duration(buf, base, ver, int(round(seconds * ts_local)))
                    pos = idx + 4
                    continue
                self.write_duration(buf, base, ver, int(round(seconds * ts)))
                pos = idx + 4

    def mvhd_timescale(self, buf):
        """获取mvhd时间刻度"""
        idx = buf.find(b'mvhd')
        ver = buf[idx + 4]
        ts_off = idx + 4 + 1 + 3 + (4 if ver == 0 else 8) + (4 if ver == 0 else 8)
        return self.read_u32(buf, ts_off)

    def read_u32(self, buf, off):
        """读取32位无符号整数"""
        import struct
        return struct.unpack_from('>I', buf, off)[0]

    def write_duration(self, buf, off, ver, ticks):
        """写入时长数据"""
        import struct
        fmt = '>I' if ver == 0 else '>Q'
        buf[off:off + struct.calcsize(fmt)] = struct.pack(fmt, ticks)


class NetworkRequestWorker(QObject):
    """网络请求工作器"""
    finished = pyqtSignal(dict)
    error = pyqtSignal(str)

    def __init__(self, url, data, headers=None):
        super().__init__()
        self.url = url
        self.data = data
        self.headers = headers or {}

    def run(self):
        try:
            # 设置请求超时和重试
            session = requests.Session()
            session.mount('http://', requests.adapters.HTTPAdapter(max_retries=3))
            session.mount('https://', requests.adapters.HTTPAdapter(max_retries=3))

            response = session.post(
                self.url,
                json=self.data,  # 使用json参数发送JSON数据
                headers=self.headers,
                timeout=(5, 10),  # (连接超时, 读取超时)
                verify=False  # 禁用SSL验证
            )

            response.raise_for_status()

            # 解析JSON响应
            response_data = response.json()
            self.finished.emit(response_data)

        except requests.exceptions.Timeout:
            self.error.emit("请求超时，请检查网络连接")
        except requests.exceptions.ConnectionError:
            self.error.emit("网络连接失败，请检查网络设置")
        except requests.exceptions.RequestException as e:
            self.error.emit(f"网络请求失败: {str(e)}")
        except json.JSONDecodeError:
            self.error.emit("响应格式错误，无法解析JSON")
        except Exception as e:
            self.error.emit(f"未知错误: {str(e)}")
        finally:
            if hasattr(self, 'session'):
                self.session.close()


class VideoApiDownloader(QThread):
    """用于从VideoAPI下载视频或图片的工作线程"""
    progress_updated = pyqtSignal(int, str)
    download_finished = pyqtSignal(bool, str, str)  # 成功状态, 消息, 临时目录路径

    def __init__(self, a_video_list, output_dir, selected_prompt="random", is_image_mode=False):
        super().__init__()
        self.a_video_list = a_video_list
        self.output_dir = output_dir
        self.selected_prompt = selected_prompt
        self.is_image_mode = is_image_mode  # 是否为图片模式（星火KS功能）
        self.is_cancelled = False
        # 使用隐藏的命名方式，以点开头并使用哈希值作为名称
        random_hash = hashlib.md5(str(time.time()).encode()).hexdigest()[:12]
        self.temp_dir = os.path.join(output_dir, f".cache_{random_hash}")
        # 下载速度信息
        self.download_speeds = {}  # 文件名 -> 速度(bytes/s)
        self.download_lock = threading.Lock()
        # 多线程配置
        self.max_threads = min(os.cpu_count(), 4)  # 限制最大线程数，避免过载
        self.thread_semaphore = threading.Semaphore(self.max_threads)
        
    def check_packet_capture_tools(self):
        """检测抓包工具"""
        # 常见抓包工具进程名列表
        capture_tools = [
            'fiddler', 'wireshark', 'charles', 'burpsuite', 'burp', 
            'httpdebugger', 'httpanalyzer', 'packetcapture', 'networkmonitor',
            'netmon', 'proxifier', 'mitmproxy', 'zap', 'telerik', 'fidder',
            'httpwatch', 'postman', 'proxycap', 'proxyman', 'proxyswitch',
            'httpproxy', 'httpsniff', 'httpsniffer', 'webdebugger', 'fiddlercore'
        ]
        
        try:
            # 获取当前所有运行进程
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    proc_name = proc.info['name'].lower()
                    # 检查进程名是否包含抓包工具关键词
                    for tool in capture_tools:
                        if tool in proc_name:
                            return True, proc_name
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    pass
            return False, ""
        except Exception as e:
            print(f"检测抓包工具时发生错误: {e}")
            return False, ""
    
    def format_speed(self, bytes_per_second):
        """格式化下载速度"""
        if bytes_per_second < 1024:
            return f"{bytes_per_second:.0f} B/s"
        elif bytes_per_second < 1024 * 1024:
            return f"{bytes_per_second / 1024:.1f} KB/s"
        else:
            return f"{bytes_per_second / (1024 * 1024):.1f} MB/s"
    
    def update_download_speed(self, filename, speed, downloaded, total):
        """更新下载速度信息（VideoAPI回调）"""
        with self.download_lock:
            self.download_speeds[filename] = speed
            
            # 计算总下载速度
            total_speed = sum(s for s in self.download_speeds.values())
            speed_text = self.format_speed(total_speed)
            
            # 计算总进度
            total_items = len(self.a_video_list)
            downloaded_items = len([f for f in self.download_speeds if self.download_speeds[f] == 0])  # 速度为0表示下载完成
            
            # 更新UI进度
            progress = int((downloaded_items / total_items) * 50)  # 下载进度占总进度的前50%
            status_text = f"阿里云分布式GPU正在处理，请稍后 ({downloaded_items}/{total_items}) - {speed_text}"
            
            # 发出更新信号
            self.progress_updated.emit(progress, status_text)
    
    def download_video_task(self, index, a_video_info, video_api, result_dict):
        """下载单个视频的任务（用于线程池）"""
        # 获取线程信号量
        with self.thread_semaphore:
            # 多次检查取消状态，确保能快速响应
            if self.is_cancelled or video_api.is_download_cancelled():
                print(f"下载任务 {index+1} 已取消")
                return

            try:
                a_video_path = a_video_info['path']
                a_filename = os.path.basename(a_video_path)

                # 再次检查取消状态
                if self.is_cancelled or video_api.is_download_cancelled():
                    print(f"下载任务 {index+1} 在获取视频信息时被取消")
                    return

                # 获取A视频分辨率
                width, height = self.get_video_resolution(a_video_path)

                # 获取A视频时长
                duration = a_video_info.get('duration', 0)

                # 计算B素材时长：A视频时长的三分之一，最大60秒
                b_duration = min(duration / 3, 60) if duration > 0 else 30

                print(f"开始下载视频/图片 {index+1}: {a_filename}")

                # 下载前最后检查取消状态
                if self.is_cancelled or video_api.is_download_cancelled():
                    print(f"下载任务 {index+1} 在开始下载前被取消")
                    return

                try:
                    if self.is_image_mode:
                        # 图片模式：下载图片
                        images = video_api.get_images_batch(count=1, image_type="pc", download=True)
                        if images and len(images) > 0:
                            # 检查是否在下载过程中被取消
                            if self.is_cancelled or video_api.is_download_cancelled():
                                print(f"图片下载任务 {index+1} 在处理过程中被取消")
                                return

                            # 将下载的图片复制到临时目录，重命名为简单序号
                            image_path = images[0]
                            ext = os.path.splitext(image_path)[1]
                            target_path = os.path.join(self.temp_dir, f"{index + 1}{ext}")
                            shutil.copy(image_path, target_path)
                            result_dict[index] = target_path
                            print(f"已下载图片: {image_path} -> {target_path}")
                    else:
                        # 视频模式：下载视频
                        print(f"开始下载视频: 提示词={self.selected_prompt}, 分辨率={width}x{height}, 时长={b_duration}秒")
                        videos = video_api.get_videos_by_prompt(
                            prompt=self.selected_prompt,
                            count=1,
                            quality="tiny",  # 使用tiny质量
                            min_width=width,  # 与A视频相同分辨率
                            min_height=height,
                            min_duration=min(b_duration, 5),  # 最小5秒
                            max_duration=b_duration + 10  # 允许一定误差
                        )

                        if videos and len(videos) > 0:
                            # 检查是否在下载过程中被取消
                            if self.is_cancelled or video_api.is_download_cancelled():
                                print(f"视频下载任务 {index+1} 在处理过程中被取消")
                                return

                            # 将下载的视频复制到临时目录，重命名为简单序号
                            video_path = videos[0]
                            target_path = os.path.join(self.temp_dir, f"{index + 1}.mp4")
                            shutil.copy(video_path, target_path)
                            result_dict[index] = target_path
                            print(f"已下载视频: {video_path} -> {target_path}")

                except Exception as e:
                    # 检查是否是取消导致的异常
                    if "下载已取消" in str(e) or self.is_cancelled or video_api.is_download_cancelled():
                        print(f"下载任务 {index+1} 被用户取消")
                        return
                    print(f"下载第 {index + 1} 个B素材失败: {e}")
            except Exception as e:
                # 检查是否是取消导致的异常
                if "下载已取消" in str(e) or self.is_cancelled or video_api.is_download_cancelled():
                    print(f"下载任务 {index+1} 被用户取消")
                    return
                print(f"视频任务处理失败 {index}: {e}")
    
    def run(self):
        try:
            # 检测抓包工具
            detected, tool_name = self.check_packet_capture_tools()
            if detected:
                print(f"检测到抓包工具: {tool_name}")
                # 清理临时目录并发送信号通知主线程显示警告并退出
                self.cleanup_temp_dir()
                self.download_finished.emit(False, "D加密出现验证问题，请检查计算机环境或联系客服，如再次出现会导致卡密被封禁。", "")
                return

            # 创建临时目录
            os.makedirs(self.temp_dir, exist_ok=True)

            # 设置临时目录为隐藏
            self.set_folder_hidden(self.temp_dir)

            # 初始化VideoAPI
            self.video_api = VideoAPI()

            # 设置下载速度回调
            self.video_api.set_download_speed_callback(self.update_download_speed)
            
            total_items = len(self.a_video_list)
            
            # 创建线程池，使用多线程同时下载
            threads = []
            # 用于存储下载结果的字典，键为索引，值为下载的文件路径
            download_results = {}
            
            # 显示初始进度
            self.progress_updated.emit(
                0, f"阿里云分布式GPU正在准备处理 (0/{total_items}) - 0 B/s"
            )
            
            # 为每个A视频创建下载线程
            for index, a_video_info in enumerate(self.a_video_list):
                if self.is_cancelled:
                    break
                    
                # 创建线程
                thread = threading.Thread(
                    target=self.download_video_task,
                    args=(index, a_video_info, self.video_api, download_results)
                )
                threads.append(thread)
                thread.start()
                
                # 每启动3个线程后，给系统一些喘息的机会
                if (index + 1) % 3 == 0:
                    time.sleep(0.5)
            
            # 等待所有线程完成，但要能快速响应取消信号
            for thread in threads:
                # 使用较短的超时时间，以便能快速检查取消状态
                while thread.is_alive():
                    thread.join(timeout=0.1)  # 每100ms检查一次
                    if self.is_cancelled or self.video_api.is_download_cancelled():
                        print("检测到取消信号，停止等待线程...")
                        break

            if self.is_cancelled or self.video_api.is_download_cancelled():
                self.cleanup_temp_dir()
                self.download_finished.emit(False, "阿里云服务器回传已取消", "")
                return
                
            # 处理下载结果
            downloaded_items = len(download_results)
            if downloaded_items == 0:
                self.cleanup_temp_dir()
                self.download_finished.emit(False, "阿里云服务器回传失败！", "")
                return
            
            # 检查是否有部分下载失败的情况
            if downloaded_items < total_items:
                print(f"警告：有 {total_items - downloaded_items} 个B素材未能成功下载")
            
            success_message = f"阿里云服务器回传成功: {downloaded_items}/{total_items} 个B素材"
            print(success_message)
            self.download_finished.emit(True, success_message, self.temp_dir)
            
        except Exception as e:
            import traceback
            print(f"单视频处理失败，详细信息：{str(e)}")
            print(traceback.format_exc())
            # 确保在异常情况下也清理临时目录
            self.cleanup_temp_dir()
            error_message = f"单视频处理失败，详细信息：{str(e)}"
            self.download_finished.emit(False, error_message, "")
    
    def get_video_resolution(self, video_path):
        """获取视频分辨率"""
        try:
            # 使用系统中可能存在的ffprobe工具
            ffprobe_paths = [
                "ffprobe",
                "ffprobe.exe",
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "2/bin/ffprobe.exe"),
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "ffmpeg-master-latest-win64-gpl-shared/bin/ffprobe.exe"),
            ]
            
            for ffprobe_path in ffprobe_paths:
                try:
                    result = subprocess.run(
                        [ffprobe_path, "-v", "quiet", "-select_streams", "v:0",
                         "-show_entries", "stream=width,height", "-of", "csv=s=x:p=0", video_path],
                        stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True,
                        creationflags=subprocess.CREATE_NO_WINDOW, timeout=5
                    )

                    if result.returncode == 0 and result.stdout.strip():
                        resolution = result.stdout.strip()
                        if 'x' in resolution:
                            width, height = resolution.split('x')
                            return int(width), int(height)
                except:
                    continue
            
            # 如果ffprobe无法使用，尝试使用MainWindow的方法
            if hasattr(self, 'ffmpeg_path') and self.ffmpeg_path:
                try:
                    ffprobe_path = self.ffmpeg_path.replace("ffmpeg.exe", "ffprobe.exe")
                    if os.path.exists(ffprobe_path):
                        result = subprocess.run(
                            [ffprobe_path, "-v", "quiet", "-select_streams", "v:0",
                             "-show_entries", "stream=width,height", "-of", "csv=s=x:p=0", video_path],
                            stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True,
                            creationflags=subprocess.CREATE_NO_WINDOW, timeout=5
                        )

                        if result.returncode == 0 and result.stdout.strip():
                            resolution = result.stdout.strip()
                            if 'x' in resolution:
                                width, height = resolution.split('x')
                                return int(width), int(height)
                except:
                    pass
                
            # 返回默认分辨率
            return 1080, 1920
        except Exception as e:
            print(f"获取视频分辨率失败: {e}")
            return 1080, 1920
    
    def cleanup_temp_dir(self):
        """清理临时目录"""
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                print(f"已清理临时目录: {self.temp_dir}")
        except Exception as e:
            print(f"清理临时目录失败: {e}")
    
    def set_folder_hidden(self, folder_path):
        """设置文件夹为隐藏"""
        try:
            if os.name == 'nt':  # 仅在Windows系统上执行
                # 使用attrib命令设置文件夹为隐藏
                subprocess.run(['attrib', '+h', folder_path], 
                              creationflags=subprocess.CREATE_NO_WINDOW,
                              check=False)
                print(f"已将文件夹设置为隐藏: {folder_path}")
        except Exception as e:
            print(f"设置文件夹隐藏属性失败: {e}")
            
    def cancel(self):
        """取消下载"""
        self.is_cancelled = True
        print("取消下载任务")

        # 立即停止VideoAPI的所有下载操作
        if hasattr(self, 'video_api') and self.video_api:
            print("正在停止VideoAPI下载...")
            self.video_api.cancel_all_downloads()

        # 确保取消时也清理临时目录
        self.cleanup_temp_dir()

def stop_processing(self):
    """停止处理"""
    try:
        if hasattr(self, 'download_worker') and self.download_worker and self.download_worker.isRunning():
            print("正在取消下载任务...")
            self.download_worker.cancel()
            self.download_worker.wait()
            print("下载任务已取消")
            
        if hasattr(self, 'worker') and self.worker:
            print("正在取消处理任务...")
            self.worker.cancel()
            self.worker.wait()
            print("处理任务已取消")

        self.is_processing = False
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.status_label.setText("处理已停止")
    except Exception as e:
        print(f"停止处理失败: {e}")
        self.status_label.setText("停止处理失败")
        
def on_process_finished(self, success, message):
    """处理完成"""
    try:
        self.is_processing = False
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)

        # 如果是单视频模式，处理完成后清理临时目录
        is_single_mode = self.single_video_checkbox.isChecked()
        if is_single_mode and hasattr(self, 'b_folder_path') and self.b_folder_path and os.path.exists(self.b_folder_path):
            try:
                # 检查是否为临时目录路径（以temp_api_开头）
                if os.path.basename(self.b_folder_path).startswith("temp_api_"):
                    print(f"准备清理临时目录: {self.b_folder_path}")
                    # 延迟1秒再删除，确保文件不再被使用
                    QTimer.singleShot(1000, lambda: self.clean_temp_dir(self.b_folder_path))
            except Exception as e:
                print(f"准备清理临时目录失败: {e}")

        if success:
            self.progress_bar.setValue(100)
            self.status_label.setText("处理完成")
            QMessageBox.information(self, "完成", message)
        else:
            self.status_label.setText("处理失败")
            QMessageBox.critical(self, "错误", message)
    except Exception as e:
        print(f"处理完成回调出错: {e}")

    def clean_temp_dir(self, dir_path):
        """清理临时目录（延迟调用）"""
        try:
            if os.path.exists(dir_path):
                shutil.rmtree(dir_path)
                print(f"已清理临时目录: {dir_path}")
        except Exception as e:
            print(f"清理临时目录失败: {e}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')

    # 设置全局字体
    font = QFont("Microsoft YaHei", 10)
    app.setFont(font)

    # 设置暗色调色板
    palette = QPalette()
    palette.setColor(QPalette.Window, QColor(26, 26, 46))
    palette.setColor(QPalette.WindowText, QColor(255, 255, 255))
    app.setPalette(palette)

    # 创建登录窗口
    login_win = login.LoginWindow()
    main_win = None

    def on_login_success(expiry_time, card_key, mute, aes_key, aes_iv):
        nonlocal main_win
        main_win = MainWindow(expiry_time, card_key, mute, aes_key, aes_iv)
        main_win.show()

        # 显示欢迎对话框
        QTimer.singleShot(500, lambda: show_welcome_dialog(expiry_time))
        login_win.close()

    def show_welcome_dialog(expiry_time):
        """显示欢迎对话框"""
        try:
            msg_box = QMessageBox(main_win)
            msg_box.setWindowTitle("欢迎使用")
            msg_box.setText(f"欢迎使用 {SOFTWARE_NAME}！\n\n您的卡密到期时间为：\n{expiry_time}\n\n疾风助您爆单！")
            msg_box.setIcon(QMessageBox.Information)
            msg_box.setStandardButtons(QMessageBox.Ok)
            msg_box.setWindowModality(Qt.ApplicationModal)

            # 设置对话框样式
            msg_box.setStyleSheet("""
                QMessageBox {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #1a1a2e, stop:1 #16213e);
                    color: white;
                    font-family: "Microsoft YaHei";
                }
                QMessageBox QLabel {
                    color: #bb86fc;
                    font-size: 16px;
                    font-weight: bold;
                    padding: 10px;
                }
                QMessageBox QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #6200ea, stop:1 #3700b3);
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 20px;
                    font-size: 14px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QMessageBox QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #7c4dff, stop:1 #6200ea);
                }
            """)

            msg_box.exec_()

        except Exception as e:
            print(f"显示欢迎对话框时出错: {e}")

    login_win.login_success.connect(on_login_success)
    login_win.show()
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
