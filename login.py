import sys
import os
import hashlib
import subprocess
import requests
import json
import configparser
import logging
import traceback
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                            QPushButton, QLabel, QLineEdit, QCheckBox, QMessageBox,
                            QTextEdit, QScrollArea)
from PyQt5.QtCore import Qt, QSettings, QThread, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, pyqtProperty, QUrl
from PyQt5.QtGui import QFont, QIcon, QPalette
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
import tempfile

# 获取系统临时目录
temp_dir = tempfile.gettempdir()
login_log_path = os.path.join(temp_dir, "WanHaoGL_login.log")

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(login_log_path, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# API配置
API_URL = "http://api2.1wxyun.com/?type=17"  # 卡密登录api（旧通道）
API_GG = "http://api2.1wxyun.com/?type=1"    # 公告api（旧通道）
API_JB = "http://api2.1wxyun.com/?type=14"   # 解绑api（旧通道）
API_DQ = "http://api2.1wxyun.com/?type=24"  # 到期时间api（旧通道）
# 新通道API
API_NEW_LOGIN = "http://************/api/keysystem/verify_key.php"  # 新登录通道
API_NEW_UNBIND = "http://************/api/keysystem/unbind.php"     # 新解绑通道
API_NEW_ANNOUNCEMENT = "http://************/api/keysystem/announcement.php"  # 新公告通道
SOFTWARE_ID = "7D1F0B1L9S1Q9G3D"            # 软件标识
VERSION = "2.0"                             # 版本号

# 新通道错误码映射
NEW_ERROR_MESSAGES = {
    "-1001": "参数错误",
    "-1002": "卡密已禁用",
    "-1003": "卡密已过期",
    "-1004": "卡密不存在",
    "-1005": "软件不存在或已禁用",
    "-1006": "版本号无效",
    "-1007": "API密钥无效",
    "-1008": "IP被禁止",
    "-1009": "需要更新版本",
    "-1010": "请求过于频繁",
    "-1011": "缺少机器码",
    "-1012": "机器码不匹配",
    "-1097": "黑名单检查失败",
    "-1098": "数据库查询错误",
    "-1099": "系统错误",
    "-1101": "解绑失败，已达到最大解绑次数",
    "-1102": "不允许解绑",
    "-1201": "积分不足",
    "-1301": "功能未授权"
}

# 错误码映射 - 完整的错误提示信息
ERROR_MESSAGES = {
    # WebApi相关错误
    "-81001": "WebApi接口不存在",
    "-81003": "软件已被强制关闭",
    "-81004": "软件不存在",
    "-81005": "软件已停用",
    "-81006": "版本号不存在",
    "-81007": "程序接口密码错误",
    "-81008": "程序不允许用户转绑",
    "-81009": "软件已开启收费模式,当前版本为免费版本",
    "-81010": "版本号已停用",
    "-81011": "软件版本必须为数字",
    "-81012": "当前软件未开启试用功能",
    "-81015": "频繁调用,请等待10分钟后再做尝试",
    "-81016": "单IP频繁访问限制",
    "-81017": "此接口开启了算法,但是提交时未加密数据再提交",
    "-81018": "变量数据不存在",
    "-81019": "变量ID必须为数字",
    "-81020": "变量别名只能是数字或者字母或者数字字母混合",
    "-81021": "错误的调用方式,请查看后台接口的调用方式和参数",
    "-81022": "机器码填写错误，长度必须是不能超过32位的字母或数字",
    "-81023": "请先登陆后再调用此方法或账号已被顶下线",
    "-81024": "扣点数量必须大于0",
    "-81025": "软件扣费模式和卡类不符",
    "-81026": "当前扣费模式为时间计费,无法使用扣点功能",
    "-81027": "黑名单已存在",
    "-81028": "当前用户在别的机器登陆使用,暂时无法登陆 请等10分钟",
    "-81029": "当前用户在别的IP登陆使用,暂时无法登陆 请等10分钟",
    "-81030": "当前用户已超过最大登陆数量,暂时无法登陆 请等10分钟",
    "-81031": "用户已在别的地方登陆",
    "-81032": "token填写错误,长度必须是16位字母或数字",
    "-81033": "用户名或单码或试用特征填写错误,长度必须是6-16位的字母或数字",
    "-81035": "解绑类型只能为数字并且只能为1或者2",
    "-81036": "用户名或单码或试用特征使用点数不足",
    "-81037": "当前版本不是最新版本,已被停止使用",
    "-81039": "用户电脑特征已被列入黑名单",
    "-81040": "检测用户状态过于频繁，请把检测周期设置大于等于3分钟",
    "-81042": "用户或卡密提交的云数据超过最大长度",
    "-81043": "用户或卡密提交的封停原因超过最大长度",
    "-81044": "提交的加入黑名单原因超过最大长度",
    "-81045": "VMP授权密钥设置错误,请查看管理端对应的设置要求进行设置",
    "-81047": "提交的VMP机器码格式错误,长度不能小于10且不能大于200",
    "-81048": "VMP授权端校验失败",
    "-81049": "取用户指定类型数据错误",
    "-81060": "超过最大可激活用户数,请升级套餐",
    "-81061": "当前软件线路过于拥挤,请稍等10分钟后再尝试登录使用",
    "-81062": "当前软件位已到期",
    "-81063": "开发者账号余额不足",
    "-81064": "开发者账号会员已到期",

    # 单码相关错误
    "-83001": "单码不存在",
    "-83002": "单码填写错误,长度应16位",
    "-83003": "单码已被锁定",
    "-83004": "单码类型和软件扣费模式不符",
    "-83005": "该单码所属卡类为单次卡类每个电脑只能登陆使用一张",
    "-83006": "单码已到期",
    "-83007": "单码使用点数不足",
    "-83008": "单码未在绑定的电脑上登陆",
    "-83009": "单码未在绑定的IP地址登陆",
    "-83011": "单码重绑次数超过限制",
    "-83012": "单码积分不足",
    "-83013": "单码卡密未激活",
    "-83014": "单码IP一致无需转绑即可登陆",
    "-83015": "单码机器码一致无需转绑即可登陆",
    "-83016": "单码转绑后将到期",
    "-83017": "单码转绑后点数小于0",
    "-83018": "单码不存在或试用特征不存在或此前已被封禁",

    # 用户相关错误
    "-82001": "用户不存在",
    "-82002": "用户名填写错误,长度必须是6-16位字母或数字",
    "-82003": "用户密码填写错误,长度必须是6-16位字母或数字",
    "-82004": "用户超级密码填写错误,长度必须是6-16位字母或数字",
    "-82005": "用户名已存在",
    "-82006": "用户已被锁定",
    "-82007": "用户已到期",
    "-82008": "用户使用点数不足",
    "-82009": "用户未在绑定的电脑上登陆",
    "-82010": "用户未在绑定的IP地址登陆",
    "-82011": "用户注册次数超过限制",
    "-82012": "用户重绑次数超过限制",
    "-82013": "用户积分不足",
    "-82014": "用户IP一致无需转绑即可登陆",
    "-82015": "用户机器码一致无需转绑即可登陆",
    "-82016": "用户注册失败，程序停止新用户注册",
    "-82017": "用户注册失败，已开启卡密注册",
    "-82018": "用户超级密码错误",
    "-82019": "用户转绑后将到期",
    "-82020": "用户转绑后点数小于0",
    "-82021": "用户密码错误",
    "-82022": "推荐人填写错误,长度必须是6-16位字母或数字",
    "-82023": "超过最大用户数量，联系管理员升级",
    "-82024": "用户不存在或者密码错误或此前已被封禁",

    # 充值卡相关错误
    "-84001": "充值卡密填写错误,长度等于16位",
    "-84002": "充值卡密不存在",
    "-84003": "充值卡密已被使用",
    "-84004": "充值卡密已被锁定",
    "-84005": "该卡密所属卡类为单次卡类每个用户只能充值一张",

    # 试用相关错误
    "-85001": "试用积分不足",
    "-85002": "试用特征不存在",
    "-85003": "试用特征已锁定",
    "-85004": "试用特征无此数据",
    "-85005": "试用特征已到期",
    "-85006": "试用特征使用点数不足",
    "-85007": "试用特征填写错误,长度必须是6-16位字母或数字"
}

class AnimatedTitleLabel(QLabel):
    """带有渐变动画效果的标题标签"""

    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self._animation_value = 0.0
        self.setup_animation()

    def setup_animation(self):
        """设置动画"""
        # 创建属性动画
        self.animation = QPropertyAnimation(self, b"animationValue")
        self.animation.setDuration(10000)  # 3秒完成一个周期
        self.animation.setStartValue(0.0)
        self.animation.setEndValue(1.0)
        self.animation.setEasingCurve(QEasingCurve.InOutSine)
        self.animation.setLoopCount(-1)  # 无限循环

        # 连接动画值变化信号
        self.animation.valueChanged.connect(self.update_gradient)

        # 启动动画
        self.animation.start()

    @pyqtProperty(float)
    def animationValue(self):
        return self._animation_value

    @animationValue.setter
    def animationValue(self, value):
        self._animation_value = value
        self.update_gradient()

    def update_gradient(self):
        """更新渐变样式"""
        # 根据动画值计算渐变色彩
        progress = self._animation_value

        # 创建动态渐变色彩
        # 粉色到蓝色的渐变，随着动画值变化
        pink_r = int(255 - progress * 100)  # 255 -> 155
        pink_g = int(182 - progress * 82)   # 182 -> 100
        pink_b = int(193 + progress * 62)   # 193 -> 255

        blue_r = int(100 + progress * 55)   # 100 -> 155
        blue_g = int(149 + progress * 106)  # 149 -> 255
        blue_b = int(237 + progress * 18)   # 237 -> 255

        # 计算渐变角度（45度到135度之间变化）
        angle = int(45 + progress * 90)

        gradient_style = f"""
            QLabel {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgb({pink_r}, {pink_g}, {pink_b}),
                    stop:0.5 rgb({int((pink_r + blue_r) / 2)}, {int((pink_g + blue_g) / 2)}, {int((pink_b + blue_b) / 2)}),
                    stop:1 rgb({blue_r}, {blue_g}, {blue_b}));
                color: white;
                font-size: 24px;
                font-weight: bold;
                border-radius: 8px;
                padding: 10px;
            }}
        """

        self.setStyleSheet(gradient_style)

class NetworkThread(QThread):
    """网络请求线程"""
    finished = pyqtSignal(object)  # 完成信号
    error = pyqtSignal(str)        # 错误信号
    
    def __init__(self, url, data, headers=None, method="POST"):
        super().__init__()
        self.url = url
        self.data = data
        self.headers = headers or {}
        self.method = method.upper()  # 请求方法，默认为POST
        self._is_running = True
        
    def run(self):
        try:
            if not self._is_running:
                return
                
            logging.debug(f"发送{self.method}请求到 {self.url}")
            
            # 设置请求超时和重试
            session = requests.Session()
            session.mount('http://', requests.adapters.HTTPAdapter(max_retries=3))
            session.mount('https://', requests.adapters.HTTPAdapter(max_retries=3))
            
            # 检查是否需要发送JSON格式数据
            is_json = False
            if self.headers and 'Content-Type' in self.headers and 'application/json' in self.headers['Content-Type']:
                is_json = True
                logging.debug(f"使用JSON格式发送数据: {self.data}")
            
            # 根据请求方法执行不同的请求
            if self.method == "GET":
                # GET请求，参数通过URL传递
                response = session.get(
                    self.url, 
                    params=self.data,
                    headers=self.headers, 
                    timeout=(5, 10),  # (连接超时, 读取超时)
                    verify=False  # 禁用SSL验证
                )
                logging.debug(f"GET请求URL: {response.url}")
            else:
                # POST请求
                response = session.post(
                    self.url, 
                    json=self.data if is_json else None,  # 使用json参数而不是data
                    data=None if is_json else self.data,
                    headers=self.headers, 
                    timeout=(5, 10),  # (连接超时, 读取超时)
                    verify=False  # 禁用SSL验证
                )
            
            if not self._is_running:
                return
                
            response.raise_for_status()
            response_text = response.text.strip()
            
            # 检查是否是错误码
            # 1. 对于旧通道，检查是否在ERROR_MESSAGES中
            # 2. 对于新通道，直接传递响应，让具体处理方法判断
            if not is_json and self.method == "POST" and response_text in ERROR_MESSAGES:
                logging.error(f"服务器返回错误: {response_text} - {ERROR_MESSAGES[response_text]}")
                self.error.emit(response_text)
                return
                
            logging.debug(f"请求成功: {response_text[:100]}")
            self.finished.emit(response)
            
        except requests.exceptions.Timeout:
            error_msg = "验证失败：请尝试重新启动或联系客服处理。"
            logging.error(f"请求超时: {error_msg}")
            self.error.emit(error_msg)
        except requests.exceptions.ConnectionError:
            error_msg = "验证失败：请尝试重新启动或联系客服处理。"
            logging.error(f"网络连接失败: {error_msg}")
            self.error.emit(error_msg)
        except requests.exceptions.RequestException as e:
            error_msg = "验证失败：请尝试重新启动或联系客服处理。"
            logging.error(f"网络请求失败: {str(e)}")
            self.error.emit(error_msg)
        except Exception as e:
            error_msg = "验证失败：请尝试重新启动或联系客服处理。"
            logging.error(f"未知错误: {str(e)}")
            self.error.emit(error_msg)
        finally:
            if hasattr(self, 'session'):
                self.session.close()
    
    def stop(self):
        """停止线程"""
        self._is_running = False
        self.wait()

def get_machine_code():
    """获取机器码 - 使用多种硬件信息组合确保唯一性"""

    def get_hardware_info():
        """获取多种硬件信息组合"""
        hardware_info = []

        # 方法1：CPU ProcessorId (优先级最高)
        try:
            logging.debug("尝试获取CPU ProcessorId")
            output = subprocess.check_output("wmic cpu get ProcessorId", shell=True, universal_newlines=True, timeout=10)
            lines = output.strip().split('\n')
            for i in range(1, len(lines)):
                line = lines[i].strip()
                if line and line != "ProcessorId":
                    hardware_info.append(f"CPU:{line}")
                    logging.debug(f"获取到CPU ProcessorId: {line[:8]}...")
                    break
        except Exception as e:
            logging.debug(f"获取CPU ProcessorId失败: {str(e)}")

        # 方法2：主板序列号
        try:
            logging.debug("尝试获取主板序列号")
            output = subprocess.check_output("wmic baseboard get SerialNumber", shell=True, universal_newlines=True, timeout=10)
            lines = output.strip().split('\n')
            for i in range(1, len(lines)):
                line = lines[i].strip()
                if line and line != "SerialNumber" and line != "To be filled by O.E.M." and len(line) > 3:
                    hardware_info.append(f"MB:{line}")
                    logging.debug(f"获取到主板序列号: {line[:8]}...")
                    break
        except Exception as e:
            logging.debug(f"获取主板序列号失败: {str(e)}")

        # 方法3：BIOS序列号
        try:
            logging.debug("尝试获取BIOS序列号")
            output = subprocess.check_output("wmic bios get SerialNumber", shell=True, universal_newlines=True, timeout=10)
            lines = output.strip().split('\n')
            for i in range(1, len(lines)):
                line = lines[i].strip()
                if line and line != "SerialNumber" and line != "To be filled by O.E.M." and len(line) > 3:
                    hardware_info.append(f"BIOS:{line}")
                    logging.debug(f"获取到BIOS序列号: {line[:8]}...")
                    break
        except Exception as e:
            logging.debug(f"获取BIOS序列号失败: {str(e)}")

        # 方法4：硬盘序列号（取第一个有效的）
        try:
            logging.debug("尝试获取硬盘序列号")
            output = subprocess.check_output("wmic diskdrive get SerialNumber", shell=True, universal_newlines=True, timeout=10)
            lines = output.strip().split('\n')
            for i in range(1, len(lines)):
                line = lines[i].strip()
                if line and line != "SerialNumber" and len(line) > 3:
                    hardware_info.append(f"DISK:{line}")
                    logging.debug(f"获取到硬盘序列号: {line[:8]}...")
                    break
        except Exception as e:
            logging.debug(f"获取硬盘序列号失败: {str(e)}")

        # 方法5：网卡MAC地址
        try:
            logging.debug("尝试获取网卡MAC地址")
            output = subprocess.check_output("wmic path win32_networkadapter where \"NetEnabled=true\" get MACAddress", shell=True, universal_newlines=True, timeout=10)
            lines = output.strip().split('\n')
            for i in range(1, len(lines)):
                line = lines[i].strip()
                if line and line != "MACAddress" and ":" in line:
                    hardware_info.append(f"MAC:{line}")
                    logging.debug(f"获取到MAC地址: {line}")
                    break
        except Exception as e:
            logging.debug(f"获取MAC地址失败: {str(e)}")

        return hardware_info

    def get_machine_code_wmic_original():
        """方法1：使用原来的WMIC命令格式（保持兼容性）"""
        try:
            logging.debug("尝试使用原来的WMIC方法获取机器码")
            output = subprocess.check_output("wmic cpu get ProcessorId", shell=True, universal_newlines=True, timeout=10)

            # 分割行并找到非空的处理器ID（兼容不同的WMIC输出格式）
            lines = output.strip().split('\n')
            processor_id = ""

            # 跳过标题行，找到第一个非空的处理器ID
            for i in range(1, len(lines)):
                line = lines[i].strip()
                if line and line != "ProcessorId":  # 跳过标题和空行
                    processor_id = line
                    break

            if processor_id:
                logging.debug(f"原来WMIC方法获取到ProcessorId: {processor_id[:8]}...")
                return processor_id
            raise Exception("原来WMIC方法输出中未找到有效的ProcessorId")
        except subprocess.CalledProcessError as e:
            # 特殊处理错误代码2147749889和类似的WMIC错误
            if e.returncode == 2147749889 or e.returncode == -2147217407:
                error_msg = (
                    f"WMIC服务异常(错误代码: {e.returncode})。"
                    "这通常是由于Windows Management Instrumentation服务问题导致的。"
                )
                logging.debug(error_msg)
                raise Exception(error_msg)
            else:
                logging.debug(f"原来WMIC命令执行失败(返回码: {e.returncode}): {str(e)}")
                raise
        except Exception as e:
            logging.debug(f"原来WMIC方法失败: {str(e)}")
            raise

    def get_machine_code_wmic_new():
        """方法2：使用新的WMIC命令格式（备用方法）"""
        try:
            logging.debug("尝试使用新的WMIC方法获取机器码")
            # 使用更兼容的WMIC命令格式
            output = subprocess.check_output(
                ["wmic", "cpu", "get", "ProcessorId", "/format:list"],
                shell=False,
                universal_newlines=True,
                timeout=10
            )
            # 解析输出，查找ProcessorId=xxx的行
            for line in output.strip().split('\n'):
                if line.startswith('ProcessorId=') and len(line) > 12:
                    processor_id = line.split('=', 1)[1].strip()
                    if processor_id:
                        logging.debug(f"新WMIC方法获取到ProcessorId: {processor_id[:8]}...")
                        return processor_id
            raise Exception("新WMIC方法输出中未找到有效的ProcessorId")
        except subprocess.CalledProcessError as e:
            # 特殊处理错误代码2147749889和类似的WMIC错误
            if e.returncode == 2147749889 or e.returncode == -2147217407:
                error_msg = (
                    f"WMIC服务异常(错误代码: {e.returncode})。"
                    "这通常是由于Windows Management Instrumentation服务问题导致的。"
                )
                logging.debug(error_msg)
                raise Exception(error_msg)
            else:
                logging.debug(f"新WMIC命令执行失败(返回码: {e.returncode}): {str(e)}")
                raise
        except Exception as e:
            logging.debug(f"新WMIC方法失败: {str(e)}")
            raise

    def get_machine_code_powershell():
        """方法2：使用PowerShell获取CPU信息"""
        try:
            logging.debug("尝试使用PowerShell获取机器码")
            ps_command = "Get-WmiObject -Class Win32_Processor | Select-Object -ExpandProperty ProcessorId"
            output = subprocess.check_output(
                ["powershell", "-Command", ps_command],
                shell=False,
                universal_newlines=True,
                timeout=10
            )
            processor_id = output.strip()
            if processor_id:
                logging.debug(f"PowerShell获取到ProcessorId: {processor_id[:8]}...")
                return processor_id
            raise Exception("PowerShell输出为空")
        except Exception as e:
            logging.debug(f"PowerShell方法失败: {str(e)}")
            raise

    # 按优先级尝试不同方法（移除注册表方法）
    methods = [
        ("原来WMIC", get_machine_code_wmic_original),
        ("新WMIC", get_machine_code_wmic_new),
        ("PowerShell", get_machine_code_powershell)
    ]

    last_error = None
    for method_name, method_func in methods:
        try:
            logging.debug(f"尝试使用{method_name}方法获取机器码")
            processor_id = method_func()

            # 使用SHA256加密
            hash_object = hashlib.sha256(processor_id.encode('utf-8'))
            machine_code = hash_object.hexdigest()[:32]
            logging.debug(f"机器码获取成功({method_name}): {machine_code[:8]}...")
            return machine_code

        except Exception as e:
            last_error = e
            logging.debug(f"{method_name}方法失败，尝试下一种方法")
            continue

    # 所有方法都失败了，提供用户友好的错误信息和解决建议
    error_msg = (
        "无法获取机器码，所有方法都失败了。\n\n"
        "可能的解决方案：\n"
        "1. 以管理员身份运行程序\n"
        "2. 检查Windows Management Instrumentation服务是否正常运行\n"
        "3. 运行以下命令修复WMIC：\n"
        "   DISM /Online /Add-Capability /CapabilityName:WMIC~~~~\n"
        "4. 重启计算机后重试\n\n"
        f"技术详情: {str(last_error)}"
    )
    logging.error(error_msg)
    raise Exception(error_msg)

class LoginWindow(QWidget):
    login_success = pyqtSignal(str, str, bool, str, str)  # 到期时间, 卡密, 静音状态, AES密钥, AES初始向量

    def __init__(self):
        try:
            super().__init__()
            logging.debug("初始化登录窗口")
            # 设置窗口置顶标志
            self.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint)
            self.initUI()
            self.load_saved_credentials()

            # 初始化背景音乐播放器
            self.init_background_music()

            # 使用QTimer延迟加载公告，确保界面先显示
            QTimer.singleShot(100, self.load_announcement)

            # 初始化线程变量
            self.announcement_thread = None
            self.login_thread = None
            self.unbind_thread = None

            self.key_input.installEventFilter(self)

        except Exception as e:
            logging.error(f"初始化登录窗口失败: {str(e)}\n{traceback.format_exc()}")
            raise
        
    def initUI(self):
        try:
            # 设置窗口基本属性
            self.setWindowTitle('欢迎使用疾风')
            self.setFixedSize(400, 580)
            self.setWindowFlags(Qt.WindowCloseButtonHint)
            
            # 设置橙白色窗口样式
            self.setStyleSheet("""
                QWidget {
                    background-color: #fff;
                    color: #333;
                }
                QLineEdit {
                    background-color: #fff;
                    color: #333;
                    border: 1px solid #FF7F24;
                    border-radius: 3px;
                    padding: 8px;
                    min-height: 20px;
                }
                QLineEdit:focus {
                    border: 1.5px solid #FFA342;
                }
                QPushButton {
                    background-color: #FF7F24;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    padding: 8px 20px;
                    min-height: 30px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #FFA342;
                }
                QPushButton:pressed {
                    background-color: #E56700;
                }
                QCheckBox {
                    color: #333;
                    spacing: 5px;
                }
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                }
                QCheckBox::indicator:unchecked {
                    border: 2px solid #FF7F24;
                    border-radius: 3px;
                    background-color: #fff;
                }
                QCheckBox::indicator:checked {
                    border: 2px solid #FF7F24;
                    border-radius: 3px;
                    background-color: #FF7F24;
                }
                QLabel {
                    color: #FF7F24;
                }
                QTextEdit {
                    background-color: #fff7f0;
                    color: #333;
                    border: 1px solid #FF7F24;
                    border-radius: 3px;
                    padding: 8px;
                }
                QScrollArea {
                    border: none;
                    background-color: transparent;
                }
                QScrollBar:vertical {
                    border: none;
                    background: #fff7f0;
                    width: 10px;
                    margin: 0px;
                }
                QScrollBar::handle:vertical {
                    background: #FFA342;
                    min-height: 20px;
                    border-radius: 5px;
                }
                QScrollBar::handle:vertical:hover {
                    background: #FF7F24;
                }
                QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                    height: 0px;
                }
                QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                    background: none;
                }
            """)
            
            # 创建主布局
            layout = QVBoxLayout()
            layout.setContentsMargins(40, 40, 40, 40)
            layout.setSpacing(20)
            
            # 标题 - 使用带动画效果的标题标签
            self.title_label = AnimatedTitleLabel('疾风3.6-风起云涌')
            self.title_label.setFont(QFont("微软雅黑", 20, QFont.Bold))
            self.title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(self.title_label)
            
            # 公告区域
            notice_label = QLabel('公告')
            notice_label.setStyleSheet('font-size: 16px; color: #FF7F24;')
            layout.addWidget(notice_label)
            
            # 创建公告文本框
            self.notice_text = QTextEdit()
            self.notice_text.setReadOnly(True)
            self.notice_text.setFixedHeight(130)
            self.notice_text.setStyleSheet("""
                QTextEdit {
                    background-color: #fff7f0;
                    color: #333;
                    border: 1px solid #FF7F24;
                    border-radius: 3px;
                    padding: 8px;
                    font-size: 16px;
                }
            """)
            layout.addWidget(self.notice_text)
            
            # 卡密输入框
            self.key_input = QLineEdit()
            self.key_input.setPlaceholderText('请输入注册码')
            self.key_input.setFont(QFont('Arial', 12))
            layout.addWidget(self.key_input)
            
            # 记住卡密选项和静音选项并排
            checkbox_layout = QHBoxLayout()
            self.remember_checkbox = QCheckBox('记住注册码')
            self.remember_checkbox.setFont(QFont('Arial', 10))
            self.mute_checkbox = QCheckBox('静音开启')
            self.mute_checkbox.setFont(QFont('Arial', 10))
            self.mute_checkbox.setChecked(False)
            checkbox_layout.addWidget(self.remember_checkbox)
            checkbox_layout.addWidget(self.mute_checkbox)
            checkbox_layout.addStretch()
            layout.addLayout(checkbox_layout)
            
            # 按钮布局
            button_layout = QHBoxLayout()
            button_layout.setSpacing(10)
            
            # 登录按钮
            self.login_button = QPushButton('登 录')
            self.login_button.setFont(QFont('Arial', 12))
            self.login_button.clicked.connect(self.login)
            button_layout.addWidget(self.login_button)
            
            # 解绑按钮
            self.unbind_button = QPushButton('解 绑')
            self.unbind_button.setFont(QFont('Arial', 12))
            self.unbind_button.setStyleSheet("""
                QPushButton {
                    background-color: #FFA342;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #FF7F24;
                }
                QPushButton:pressed {
                    background-color: #E56700;
                }
            """)
            self.unbind_button.clicked.connect(self.unbind)
            button_layout.addWidget(self.unbind_button)
            
            layout.addLayout(button_layout)
            
            # 添加弹性空间
            layout.addStretch()

            # 用户协议链接
            agreement_label = QLabel('<a href="http://************/user_agreement.html" style="color: #0066CC; text-decoration: none;">查看「疾风系列」软件用户协议</a>')
            agreement_label.setStyleSheet('color: #0066CC; font-size: 15px;')
            agreement_label.setAlignment(Qt.AlignCenter)
            agreement_label.setOpenExternalLinks(True)  # 允许打开外部链接
            layout.addWidget(agreement_label)

            # 底部版权信息
            copyright_label = QLabel('\n芥末章鱼™ 以及 Denuvo Anti-Tamper - 提供安全服务')
            copyright_label.setStyleSheet('color: black; font-size: 12px;')
            copyright_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(copyright_label)
            
            self.setLayout(layout)
            logging.debug("UI初始化完成")

        except Exception as e:
            logging.error(f"UI初始化失败: {str(e)}\n{traceback.format_exc()}")
            raise

    def init_background_music(self):
        """初始化背景音乐播放器"""
        try:
            # 创建音乐播放器
            self.music_player = QMediaPlayer()

            # 检查音乐文件是否存在
            music_file = "dazhanhongtu.mp3"
            if os.path.exists(music_file):
                # 设置音乐文件
                music_url = QUrl.fromLocalFile(os.path.abspath(music_file))
                self.music_player.setMedia(QMediaContent(music_url))

                # 设置音量（0-100）
                self.music_player.setVolume(50)

                # 检查静音设置
                settings = QSettings('WanHaoGL', 'LiveAssistant')
                is_muted = settings.value('mute', False, type=bool)

                if not is_muted:
                    # 连接播放结束信号，实现循环播放
                    self.music_player.mediaStatusChanged.connect(self.on_music_status_changed)

                    # 开始播放
                    self.music_player.play()
                    logging.debug("背景音乐开始播放")
                else:
                    logging.debug("静音模式，不播放背景音乐")
            else:
                logging.warning(f"背景音乐文件不存在: {music_file}")

        except Exception as e:
            logging.error(f"初始化背景音乐失败: {str(e)}")
            # 音乐播放失败不影响登录功能
            self.music_player = None

    def on_music_status_changed(self, status):
        """音乐状态改变时的处理"""
        try:
            if status == QMediaPlayer.EndOfMedia:
                # 音乐播放结束，重新开始播放实现循环
                self.music_player.setPosition(0)
                self.music_player.play()
        except Exception as e:
            logging.error(f"处理音乐状态改变失败: {str(e)}")

    def stop_background_music(self):
        """停止背景音乐"""
        try:
            if hasattr(self, 'music_player') and self.music_player:
                self.music_player.stop()
                logging.debug("背景音乐已停止")
        except Exception as e:
            logging.error(f"停止背景音乐失败: {str(e)}")

    def closeEvent(self, event):
        """处理窗口关闭事件"""
        try:
            logging.debug("窗口关闭")
            # 停止背景音乐
            self.stop_background_music()
            # 停止所有网络线程
            for thread in [self.announcement_thread, self.login_thread, self.unbind_thread]:
                if thread and thread.isRunning():
                    thread.stop()
            # 清理资源
            self.cleanup()
            event.accept()
        except Exception as e:
            logging.error(f"窗口关闭处理失败: {str(e)}")
            event.accept()

    def cleanup(self):
        """清理资源"""
        try:
            logging.debug("开始清理资源")
            # 停止背景音乐
            self.stop_background_music()
            # 停止标题动画
            if hasattr(self, 'title_label') and hasattr(self.title_label, 'animation'):
                self.title_label.animation.stop()
            # 清理网络线程
            for thread in [self.announcement_thread, self.login_thread, self.unbind_thread]:
                if thread and thread.isRunning():
                    thread.stop()
            logging.debug("资源清理完成")
        except Exception as e:
            logging.error(f"资源清理失败: {str(e)}")

    def load_saved_credentials(self):
        """加载保存的卡密"""
        try:
            settings = QSettings('WanHaoGL', 'LiveAssistant')
            saved_key = settings.value('saved_key', '')
            remember = settings.value('remember_key', False, type=bool)
            mute = settings.value('mute', False, type=bool)
            
            if remember and saved_key:
                self.key_input.setText(saved_key)
                self.remember_checkbox.setChecked(True)
                self.mute_checkbox.setChecked(mute)
                logging.debug("已加载保存的卡密")
        except Exception as e:
            logging.error(f"加载保存的卡密失败: {str(e)}")
            
    def save_credentials(self):
        """保存卡密"""
        try:
            settings = QSettings('WanHaoGL', 'LiveAssistant')
            if self.remember_checkbox.isChecked():
                settings.setValue('saved_key', self.key_input.text())
                settings.setValue('remember_key', True)
                settings.setValue('mute', self.mute_checkbox.isChecked())
                logging.debug("已保存卡密")
            else:
                settings.remove('saved_key')
                settings.setValue('remember_key', False)
                settings.setValue('mute', self.mute_checkbox.isChecked())
                logging.debug("已清除保存的卡密")
        except Exception as e:
            logging.error(f"保存卡密失败: {str(e)}")
            
    def load_announcement(self):
        """加载公告"""
        try:
            # 先显示默认公告
            self.notice_text.setText("正在加载公告...\n")
            
            # 使用新的公告API，通过GET请求获取
            params = {'software_identifier': SOFTWARE_ID}
            
            # 创建并启动网络线程，使用GET方法
            self.announcement_thread = NetworkThread(
                API_NEW_ANNOUNCEMENT, 
                params,
                method="GET"  # 使用GET请求
            )
            self.announcement_thread.finished.connect(self.handle_announcement)
            self.announcement_thread.error.connect(self.handle_announcement_error)
            self.announcement_thread.start()
            logging.debug("开始加载公告（GET请求）")
        except Exception as e:
            logging.error(f"加载公告失败: {str(e)}")
            self.notice_text.setText("公告加载失败，请稍后重试")
            
    def handle_announcement(self, response):
        """处理公告响应"""
        try:
            # 检查是否是错误码
            response_text = response.text.strip()
            if response_text.startswith("-") and response_text[1:].isdigit():
                # 这是一个错误码
                logging.error(f"获取公告失败，错误码: {response_text}")
                self.notice_text.setText("公告加载失败，请稍后重试")
                return
                
            # 尝试解析JSON响应
            try:
                response_data = response.json()
                announcement = response_data.get("announcement", "")
                if announcement:
                    self.notice_text.setText(announcement)
                    logging.debug("公告加载成功（新通道）")
                    return
                else:
                    self.notice_text.setText("暂无公告")
                    logging.debug("公告为空（新通道）")
                    return
            except:
                # 如果不是JSON格式，尝试作为旧通道响应处理
                announcement = response.text
                self.notice_text.setText(announcement)
                logging.debug("公告加载成功（旧通道）")
        except Exception as e:
            logging.error(f"处理公告失败: {str(e)}")
            self.handle_announcement_error(f"处理公告失败: {str(e)}")
            
    def handle_announcement_error(self, error_msg):
        """处理公告加载错误"""
        try:
            logging.error(f"公告加载错误: {error_msg}")
            self.notice_text.setText("公告加载失败，请稍后重试")
        except Exception as e:
            logging.error(f"处理公告错误失败: {str(e)}")
            
    def handle_error(self, error_msg):
        """处理一般错误"""
        try:
            logging.error(error_msg)
            # 检查是否是已知的错误码
            if error_msg in ERROR_MESSAGES:
                QMessageBox.critical(self, "验证失败", ERROR_MESSAGES[error_msg])
            elif error_msg in NEW_ERROR_MESSAGES:
                QMessageBox.critical(self, "验证失败", NEW_ERROR_MESSAGES[error_msg])
            else:
                # 统一错误提示
                QMessageBox.critical(self, "验证失败", "验证失败：请尝试重新启动或联系客服处理。")
        except Exception as e:
            logging.error(f"处理错误失败: {str(e)}")
            QMessageBox.critical(self, "验证失败", "验证失败：请尝试重新启动或联系客服处理。")
            
    def login(self):
        """处理登录逻辑"""
        try:
            key = self.key_input.text().strip()
            
            if not key:
                QMessageBox.warning(self, '提示', '请输入注册码')
                return
                
            # 禁用登录按钮，防止重复点击
            self.login_button.setEnabled(False)
            self.login_button.setText('登录中...')
            
            try:
                machine_code = get_machine_code()
            except Exception as e:
                self.handle_error(str(e))
                self.reset_login_button()
                return
            
            # 根据卡密长度选择不同的登录通道
            key_length = len(key)
            
            if key_length == 16:
                # 使用旧通道（16位卡密）
                logging.debug("使用旧通道登录（16位卡密）")
                data = {
                    "Softid": SOFTWARE_ID,
                    "Card": key,
                    "Version": VERSION,
                    "Mac": machine_code
                }
                self.login_thread = NetworkThread(API_URL, data)
                self.login_thread.finished.connect(lambda response: self.handle_login_response(response, key, False))
            elif 17 <= key_length <= 32:
                # 使用新通道（17-32位卡密）
                logging.debug("使用新通道登录（17-32位卡密）")
                data = {
                    "software_identifier": SOFTWARE_ID,
                    "key_code": key,
                    "machine_code": machine_code
                }
                headers = {"Content-Type": "application/json;charset=UTF-8"}
                self.login_thread = NetworkThread(API_NEW_LOGIN, data, headers)
                self.login_thread.finished.connect(lambda response: self.handle_login_response(response, key, True))
            else:
                # 卡密长度不符合要求
                QMessageBox.warning(self, '提示', '卡密格式不正确，请输入有效的卡密')
                self.reset_login_button()
                return
                
            # 连接错误处理信号
            self.login_thread.error.connect(self.handle_login_error)
            self.login_thread.start()
            logging.debug("开始登录请求")
            
        except Exception as e:
            logging.error(f"登录处理失败: {str(e)}")
            self.handle_error(f"登录处理失败: {str(e)}")
            self.reset_login_button()

    def reset_login_button(self):
        """重置登录按钮状态"""
        try:
            self.login_button.setEnabled(True)
            self.login_button.setText('登 录')
        except Exception as e:
            logging.error(f"重置登录按钮失败: {str(e)}")

    def handle_login_error(self, error_msg):
        """处理登录错误"""
        try:
            logging.error(f"登录错误: {error_msg}")
            self.handle_error(error_msg)
            self.reset_login_button()
        except Exception as e:
            logging.error(f"处理登录错误失败: {str(e)}")
            self.reset_login_button()

    def handle_login_response(self, response, key, is_new_api):
        """处理登录响应"""
        try:
            if is_new_api:
                # 处理新通道响应（JSON格式）
                try:
                    # 首先检查响应是否为纯数字错误码
                    response_text = response.text.strip()
                    if response_text.startswith("-") and response_text[1:].isdigit():
                        # 这是一个错误码
                        error_code = response_text
                        if error_code in NEW_ERROR_MESSAGES:
                            QMessageBox.critical(self, '验证失败', NEW_ERROR_MESSAGES[error_code])
                        else:
                            QMessageBox.critical(self, '验证失败', f'验证失败：错误码 {error_code}')
                        self.reset_login_button()
                        return
                        
                    # 尝试解析JSON响应
                    response_data = response.json()
                    if "token" in response_data and "expires" in response_data:
                        # 新通道登录成功
                        token = response_data["token"]
                        expiry_time_str = response_data["expires"]
                        
                        # 直接完成登录流程
                        self.expiry_time_str = expiry_time_str
                        self.card_key = key
                        
                        # 获取AES密钥
                        self.get_aes_key(key, is_new_api)
                        return
                    else:
                        # 检查错误码
                        error_code = str(response_data.get("error", ""))
                        if error_code in NEW_ERROR_MESSAGES:
                            QMessageBox.critical(self, '验证失败', NEW_ERROR_MESSAGES[error_code])
                        else:
                            QMessageBox.critical(self, '验证失败', '验证失败：请尝试重新启动或联系客服处理。')
                        self.reset_login_button()
                        return
                except Exception as e:
                    logging.error(f"解析新通道响应失败: {str(e)}")
                    # 尝试直接显示响应内容
                    response_text = response.text.strip()
                    if response_text.startswith("-") and response_text[1:].isdigit():
                        # 这是一个错误码
                        error_code = response_text
                        if error_code in NEW_ERROR_MESSAGES:
                            QMessageBox.critical(self, '验证失败', NEW_ERROR_MESSAGES[error_code])
                        else:
                            QMessageBox.critical(self, '验证失败', f'验证失败：错误码 {error_code}')
                    else:
                        QMessageBox.critical(self, '验证失败', '验证失败：请尝试重新启动或联系客服处理。')
                    self.reset_login_button()
                    return
            else:
                # 处理旧通道响应（文本格式）
                token = response.text.strip()
                
                # 检查是否是错误码
                if token in ERROR_MESSAGES:
                    QMessageBox.critical(self, '验证失败', ERROR_MESSAGES[token])
                    self.reset_login_button()
                    return

                if not token or not token.isalnum():
                    QMessageBox.critical(self, '验证失败', '验证失败：请尝试重新启动或联系客服处理。')
                    self.reset_login_button()
                    return
                    
                # 检查到期时间
                data = {
                    "Softid": SOFTWARE_ID,
                    "UserName": key,
                    "UserPwd": ""  # 单码时留空
                }
                
                # 创建并启动到期时间检查线程
                self.expiry_thread = NetworkThread(API_DQ, data)
                self.expiry_thread.finished.connect(lambda r: self.handle_expiry_check(r, token, key, is_new_api))
                self.expiry_thread.error.connect(self.handle_login_error)
                self.expiry_thread.start()
                logging.debug("开始检查到期时间")
            
        except Exception as e:
            logging.error(f"处理登录响应失败: {str(e)}")
            self.handle_error(f"处理登录响应失败: {str(e)}")
            self.reset_login_button()

    def handle_expiry_check(self, response, token, key, is_new_api):
        """处理到期时间检查"""
        try:
            # 如果是新通道，expiry_time_str已经在handle_login_response中设置
            if is_new_api:
                # 获取AES密钥
                self.get_aes_key(key, is_new_api)
                return
                
            # 处理旧通道响应
            expiry_status = response.text.strip()
            
            # 检查是否是错误码
            if expiry_status in ERROR_MESSAGES:
                QMessageBox.critical(self, '验证失败', ERROR_MESSAGES[expiry_status])
                self.reset_login_button()
                return
                
            # 处理到期时间
            expiry_time_str = ""
            if expiry_status and expiry_status != "":
                try:
                    expiry_time = datetime.strptime(expiry_status, "%Y-%m-%d %H:%M:%S")
                    expiry_time_str = expiry_time.strftime('%Y-%m-%d %H:%M:%S')
                    message = f"登录成功！\n您的卡密到期时间为: {expiry_time_str}"
                except ValueError:
                    expiry_time_str = expiry_status
                    message = "登录成功！"
            else:
                message = "登录成功！"
            
            # 保存临时变量，等待AES密钥获取完成后使用
            self.expiry_time_str = expiry_time_str
            self.card_key = key
            
            # 获取AES密钥
            self.get_aes_key(key, is_new_api)
            
            logging.debug("登录成功（旧通道）")
            
        except Exception as e:
            logging.error(f"检查到期时间失败: {str(e)}")
            self.handle_error(f"检查到期时间失败: {str(e)}")
            self.reset_login_button()

    def get_aes_key(self, key, is_new_api):
        """获取AES加密密钥"""
        try:
            logging.debug("开始获取AES密钥")
            
            # 准备请求数据
            api_url = "http://************/api/verify.php"
            data = {
                "key": key,
                "feature_code": "vmp"
            }
            
            # 设置请求头
            headers = {
                "Content-Type": "application/json;charset=UTF-8"
            }
            
            # 创建并启动网络线程
            self.aes_key_thread = NetworkThread(api_url, data, headers)
            self.aes_key_thread.finished.connect(self.handle_aes_key_response)
            self.aes_key_thread.error.connect(self.handle_aes_key_error)
            self.aes_key_thread.start()
            
        except Exception as e:
            logging.error(f"获取AES密钥请求失败: {str(e)}")
            # 即使AES密钥获取失败，仍然继续登录流程
            self.complete_login(self.expiry_time_str, self.card_key, "", "")

    def handle_aes_key_response(self, response):
        """处理AES密钥响应"""
        try:
            response_data = response.json()
            
            if response_data.get("status") == True:
                # 获取完整密钥
                full_code = response_data.get("data", {}).get("code", "")
                
                # 分割密钥和初始向量
                if len(full_code) >= 48:
                    aes_key = full_code[:32]
                    aes_iv = full_code[32:48]
                    
                    logging.debug(f"AES密钥获取成功")
                    print(f"AES1: {aes_key}")
                    print(f"AES2: {aes_iv}")
                    
                    # 存储密钥，等待发送给主窗口
                    self.aes_key = aes_key
                    self.aes_iv = aes_iv
                    
                    # 执行登录成功流程，传递AES密钥
                    self.complete_login(self.expiry_time_str, self.card_key, aes_key, aes_iv)
                else:
                    logging.error(f"AES密钥格式错误: {full_code}")
                    # 执行登录成功流程，但传递空密钥
                    self.complete_login(self.expiry_time_str, self.card_key, "", "")
            else:
                logging.error(f"获取AES密钥失败: {response_data.get('message', '未知错误')}")
                # 执行登录成功流程，但传递空密钥
                self.complete_login(self.expiry_time_str, self.card_key, "", "")
                
        except Exception as e:
            logging.error(f"处理AES密钥响应失败: {str(e)}")
            # 执行登录成功流程，但传递空密钥
            self.complete_login(self.expiry_time_str, self.card_key, "", "")

    def handle_aes_key_error(self, error_msg):
        """处理AES密钥获取错误"""
        logging.error(f"获取AES密钥失败: {error_msg}")
        # 执行登录成功流程，但传递空密钥
        self.complete_login(self.expiry_time_str, self.card_key, "", "")

    def complete_login(self, expiry_time_str, card_key, aes_key="", aes_iv=""):
        """完成登录流程，发送信号进入主界面"""
        # 停止背景音乐
        self.stop_background_music()

        # 保存卡密设置
        self.save_credentials()

        # 发射信号，传递到期时间、卡密、静音状态和AES密钥
        self.login_success.emit(expiry_time_str, card_key, self.mute_checkbox.isChecked(), aes_key, aes_iv)

        # 隐藏登录窗口
        self.hide()

    def unbind(self):
        """处理解绑逻辑"""
        try:
            key = self.key_input.text().strip()
            if not key:
                QMessageBox.warning(self, '提示', '请输入注册码以进行解绑')
                return
            
            # 根据卡密长度判断使用哪个通道
            key_length = len(key)
            
            if key_length == 16:
                # 16位卡密不支持手动解绑
                QMessageBox.warning(self, '提示', '旧版卡密暂不支持手动解绑，请联系客服。')
                return
            elif 17 <= key_length <= 32:
                # 17-32位卡密使用新通道解绑
                reply = QMessageBox.question(self, '确认解绑', 
                                           '确定要解绑当前设备吗？\n解绑后需要重新输入注册码。',
                                           QMessageBox.Yes | QMessageBox.No, 
                                           QMessageBox.No)
                
                if reply == QMessageBox.Yes:
                    # 禁用解绑按钮
                    self.unbind_button.setEnabled(False)
                    self.unbind_button.setText('解绑中...')
                    
                    try:
                        machine_code = get_machine_code()
                    except Exception as e:
                        self.handle_error(str(e))
                        self.reset_unbind_button()
                        return
                        
                    # 使用新通道解绑
                    data = {
                        "key_code": key,
                        "software_identifier": SOFTWARE_ID,
                        "machine_code": machine_code
                    }
                    
                    headers = {"Content-Type": "application/json;charset=UTF-8"}
                    
                    # 创建并启动解绑线程
                    self.unbind_thread = NetworkThread(API_NEW_UNBIND, data, headers)
                    self.unbind_thread.finished.connect(self.handle_unbind_response_new)
                    self.unbind_thread.error.connect(self.handle_unbind_error)
                    self.unbind_thread.start()
                    logging.debug("开始解绑请求（新通道）")
            else:
                # 卡密长度不符合要求
                QMessageBox.warning(self, '提示', '卡密格式不正确，请输入有效的卡密')
                return
                
        except Exception as e:
            logging.error(f"解绑处理失败: {str(e)}")
            self.handle_error(f"解绑处理失败: {str(e)}")
            self.reset_unbind_button()

    def reset_unbind_button(self):
        """重置解绑按钮状态"""
        try:
            self.unbind_button.setEnabled(True)
            self.unbind_button.setText('解 绑')
        except Exception as e:
            logging.error(f"重置解绑按钮失败: {str(e)}")

    def handle_unbind_error(self, error_msg):
        """处理解绑错误"""
        try:
            logging.error(f"解绑错误: {error_msg}")
            self.handle_error(error_msg)
            self.reset_unbind_button()
        except Exception as e:
            logging.error(f"处理解绑错误失败: {str(e)}")
            self.reset_unbind_button()

    def handle_unbind_response_new(self, response):
        """处理新通道解绑响应"""
        try:
            # 首先检查响应是否为纯数字错误码
            response_text = response.text.strip()
            if response_text.startswith("-") and response_text[1:].isdigit():
                # 这是一个错误码
                error_code = response_text
                if error_code in NEW_ERROR_MESSAGES:
                    QMessageBox.critical(self, '解绑失败', NEW_ERROR_MESSAGES[error_code])
                else:
                    QMessageBox.critical(self, '解绑失败', f'解绑失败：错误码 {error_code}')
                return
                
            # 解析JSON响应
            try:
                response_data = response.json()
                
                if response_data.get("success") == True:
                    # 解绑成功
                    remaining_unbinds = response_data.get("remaining_unbinds", 0)
                    
                    # 清除保存的卡密
                    settings = QSettings('WanHaoGL', 'LiveAssistant')
                    settings.remove('saved_key')
                    settings.setValue('remember_key', False)
                    
                    # 清空输入框
                    self.key_input.clear()
                    self.remember_checkbox.setChecked(False)
                    
                    QMessageBox.information(self, '解绑成功', f'设备已解绑，请在新设备软件重新输入注册码。\n剩余解绑次数：{remaining_unbinds}')
                    logging.debug(f"解绑成功（新通道），剩余次数：{remaining_unbinds}")
                else:
                    # 解绑失败
                    error_message = response_data.get("message", "验证失败：请尝试重新启动或联系客服处理。")
                    QMessageBox.critical(self, '解绑失败', error_message)
                    logging.warning(f"解绑失败（新通道）: {error_message}")
            except Exception as e:
                logging.error(f"解析新通道解绑响应失败: {str(e)}")
                # 尝试直接显示响应内容
                if response_text.startswith("-") and response_text[1:].isdigit():
                    # 这是一个错误码
                    error_code = response_text
                    if error_code in NEW_ERROR_MESSAGES:
                        QMessageBox.critical(self, '解绑失败', NEW_ERROR_MESSAGES[error_code])
                    else:
                        QMessageBox.critical(self, '解绑失败', f'解绑失败：错误码 {error_code}')
                else:
                    QMessageBox.critical(self, '验证失败', '验证失败：请尝试重新启动或联系客服处理。')
        except Exception as e:
            logging.error(f"处理解绑响应失败: {str(e)}")
            self.handle_error(f"处理解绑响应失败: {str(e)}")
        finally:
            self.reset_unbind_button()

    def eventFilter(self, obj, event):
        # 禁止卡密输入框输入空格
        if obj == self.key_input:
            if event.type() == event.KeyPress:
                if event.key() in (Qt.Key_Space,):
                    return True  # 忽略空格键
        return super().eventFilter(obj, event)

def main():
    try:
        # 确保只有一个QApplication实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 设置异常处理
        def exception_hook(exctype, value, traceback):
            logging.error(f"未捕获的异常: {exctype.__name__}: {value}\n{traceback.format_exc()}")
            sys.__excepthook__(exctype, value, traceback)
        
        sys.excepthook = exception_hook
        
        # 创建并显示登录窗口
        login_window = LoginWindow()
        login_window.show()
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except Exception as e:
        logging.critical(f"程序启动失败: {str(e)}\n{traceback.format_exc()}")
        sys.exit(1)

if __name__ == '__main__':
    main()

