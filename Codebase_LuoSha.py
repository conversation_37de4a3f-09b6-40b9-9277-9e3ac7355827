import os
import subprocess
import json
from PyQt5.QtCore import QThread, pyqtSignal

class LuoShaProcessor(QThread):
    """罗莎（DY）3.6新通道处理器"""
    
    progress_updated = pyqtSignal(int, str)
    process_finished = pyqtSignal(bool, str)
    
    def __init__(self, ffmpeg_path, a_video_list, b_video_list, output_dir, decrypted_code, delete_used_b):
        super().__init__()
        self.ffmpeg_path = ffmpeg_path
        self.a_video_list = a_video_list
        self.b_video_list = b_video_list
        self.output_dir = output_dir
        self.decrypted_code = decrypted_code
        self.delete_used_b = delete_used_b
        self.is_running = True
        
        # 加载服务器配置
        self.config = self.load_server_config()
    
    def load_server_config(self):
        """加载服务器端配置"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), "luosha_dy_config.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    print(f"[罗莎] 成功加载配置: {list(config.keys())}")
                    return config
            else:
                print(f"[罗莎] 配置文件不存在: {config_path}")
                return {}
        except Exception as e:
            print(f"[罗莎] 配置加载失败: {e}")
            return {}
    
    def cancel(self):
        """取消处理"""
        self.is_running = False

    def stop(self):
        """停止处理"""
        self.is_running = False
    
    def get_video_info(self, video_path):
        """获取视频信息（分辨率和时长）"""
        try:
            # 如果传入的是字典，提取路径
            if isinstance(video_path, dict):
                actual_path = video_path['path']
                duration = video_path.get('duration', 0)
            else:
                actual_path = video_path
                duration = 0

            cmd = [
                self.ffmpeg_path.replace('ffmpeg.exe', 'ffprobe.exe'),
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                actual_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True,
                                  creationflags=subprocess.CREATE_NO_WINDOW,
                                  encoding='utf-8', errors='ignore')

            if result.returncode == 0:
                info = json.loads(result.stdout)
                video_stream = None
                for stream in info['streams']:
                    if stream['codec_type'] == 'video':
                        video_stream = stream
                        break

                if video_stream:
                    width = int(video_stream['width'])
                    height = int(video_stream['height'])
                    if duration == 0:
                        duration = float(video_stream.get('duration', 0))
                    return width, height, duration

            # 如果ffprobe失败，使用默认值
            return 576, 1024, duration if duration > 0 else 10.0

        except Exception as e:
            print(f"获取视频信息失败: {e}")
            return 576, 1024, duration if duration > 0 else 10.0
    
    def step1_process(self, input_path, output_path):
        """第一步：处理左视频 - fps=60"""
        if not self.config.get('step1'):
            print("缺少step1配置")
            return False
            
        step1_config = self.config['step1']
        
        cmd = [
            self.ffmpeg_path, '-y', '-i', input_path,
            '-vf', f'fps={step1_config.get("fps", "60")}',
            '-c:v', step1_config.get('codec', 'libx264'),
            '-preset', step1_config.get('preset', 'medium'),
            '-x264-params', f'crf={step1_config.get("crf", "23")}',
            '-profile:v', step1_config.get('profile', 'main'),
            '-pix_fmt', 'yuv420p',
            '-movflags', '+faststart',
            '-flags', '+cgop',
            '-tune', step1_config.get('tune', 'ssim'),
            '-c:a', 'copy',
            output_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, 
                                  creationflags=subprocess.CREATE_NO_WINDOW,
                                  encoding='utf-8', errors='ignore')
            return result.returncode == 0
        except Exception as e:
            print(f"Step1处理失败: {e}")
            return False
    
    def step2_process(self, input_path, output_path, width, height):
        """第二步：提取前0.333秒并缩放"""
        if not self.config.get('step2'):
            print("缺少step2配置")
            return False
            
        step2_config = self.config['step2']
        
        cmd = [
            self.ffmpeg_path, '-y', '-i', input_path,
            '-ss', '00:00:00', '-t', step2_config.get('duration', '0.333'),
            '-vf', f'scale={width}:{height}:force_original_aspect_ratio=decrease,pad={width}:{height}:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps={step2_config.get("fps", "60")}',
            '-c:v', step2_config.get('codec', 'libx264'),
            '-preset', step2_config.get('preset', 'medium'),
            '-x264-params', f'crf={step2_config.get("crf", "23")}',
            '-profile:v', step2_config.get('profile', 'main'),
            '-pix_fmt', 'yuv420p',
            '-movflags', '+faststart',
            '-flags', '+cgop',
            '-tune', step2_config.get('tune', 'ssim'),
            '-an',
            output_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True,
                                  creationflags=subprocess.CREATE_NO_WINDOW,
                                  encoding='utf-8', errors='ignore')
            return result.returncode == 0
        except Exception as e:
            print(f"Step2处理失败: {e}")
            return False
    
    def step3_process(self, input_path, output_path, width, height, duration):
        """第三步：处理右视频 - 循环播放并裁剪"""
        if not self.config.get('step3'):
            print("缺少step3配置")
            return False
            
        step3_config = self.config['step3']
        # 计算实际时长 (总时长 - 0.333)
        actual_duration = duration - 0.333
        
        cmd = [
            self.ffmpeg_path, '-y', '-stream_loop', '-1', '-i', input_path,
            '-map_metadata', '-1',
            '-t', str(actual_duration),
            '-vf', f'scale={width}:{height}:force_original_aspect_ratio=disable,setsar=1:1,fps={step3_config.get("fps", "60")}',
            '-c:v', step3_config.get('codec', 'libx264'),
            '-preset', step3_config.get('preset', 'medium'),
            '-x264-params', f'crf={step3_config.get("crf", "23")}',
            '-profile:v', step3_config.get('profile', 'main'),
            '-pix_fmt', 'yuv420p',
            '-movflags', '+faststart',
            '-flags', '+cgop',
            '-tune', step3_config.get('tune', 'ssim'),
            '-an',
            output_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True,
                                  creationflags=subprocess.CREATE_NO_WINDOW,
                                  encoding='utf-8', errors='ignore')
            return result.returncode == 0
        except Exception as e:
            print(f"Step3处理失败: {e}")
            return False
    
    def step4_process(self, left2_path, right1_path, output_path, duration):
        """第四步：连接left_2和right_1"""
        if not self.config.get('step4'):
            print("缺少step4配置")
            return False
            
        step4_config = self.config['step4']
        # 计算实际时长 (总时长 - 0.333)
        actual_duration = duration - 0.333
        
        cmd = [
            self.ffmpeg_path, '-y', '-i', left2_path, '-i', right1_path,
            '-filter_complex', f'[0:v][1:v]concat=n=2:v=1:a=0,trim=duration={actual_duration}',
            '-c:v', step4_config.get('codec', 'libx264'),
            '-preset', step4_config.get('preset', 'medium'),
            '-x264-params', f'crf={step4_config.get("crf", "23")}',
            '-profile:v', step4_config.get('profile', 'main'),
            '-pix_fmt', 'yuv420p',
            '-movflags', '+faststart',
            '-flags', '+cgop',
            '-tune', step4_config.get('tune', 'ssim'),
            '-an',
            output_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True,
                                  creationflags=subprocess.CREATE_NO_WINDOW,
                                  encoding='utf-8', errors='ignore')
            return result.returncode == 0
        except Exception as e:
            print(f"Step4处理失败: {e}")
            return False
    
    def step5_process(self, left1_path, left3_path, output_path, duration):
        """第五步：最终交错处理"""
        if not self.config.get('step5'):
            print("缺少step5配置")
            return False
            
        step5_config = self.config['step5']
        # 计算实际时长 (总时长 - 0.333)
        actual_duration = duration - 0.333
        
        cmd = [
            self.ffmpeg_path, '-y', '-i', left1_path, '-i', left3_path,
            '-filter_complex', "[0:v]setsar=1/1,select='not(mod(n,2))'[a];[1:v]setsar=1/1,select='mod(n,2)'[b];[a][b]interleave,select='not(eq(n,0))'[v]",
            '-map', '[v]',
            '-c:v', step5_config.get('codec', 'libx264'),
            '-preset', step5_config.get('preset', 'medium'),
            '-x264-params', f'crf={step5_config.get("crf", "23")}',
            '-profile:v', step5_config.get('profile', 'main'),
            '-pix_fmt', 'yuv420p',
            '-movflags', '+faststart',
            '-flags', '+cgop',
            '-tune', step5_config.get('tune', 'ssim'),
            '-vsync', 'vfr',
            '-video_track_timescale', '1000000',
            '-map', '0:a?',
            '-c:a', 'copy',
            '-t', str(actual_duration),
            output_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True,
                                  creationflags=subprocess.CREATE_NO_WINDOW,
                                  encoding='utf-8', errors='ignore')
            return result.returncode == 0
        except Exception as e:
            print(f"Step5处理失败: {e}")
            return False

    def run(self):
        """主处理流程"""
        try:
            if not self.config:
                print("配置加载失败，无法处理")
                self.process_finished.emit(False, "配置加载失败")
                return

            total_videos = len(self.a_video_list)
            if total_videos == 0:
                self.process_finished.emit(False, "没有A视频需要处理")
                return

            success_count = 0

            for i, a_video_info in enumerate(self.a_video_list):
                if not self.is_running:
                    break

                try:
                    # 获取A视频信息
                    width, height, duration = self.get_video_info(a_video_info)
                    if not width or not height or not duration:
                        print(f"无法获取视频信息: {a_video_info}")
                        continue

                    # 获取文件名和路径
                    a_video_path = a_video_info['path']
                    a_video_filename = os.path.splitext(a_video_info['filename'])[0]
                    output_filename = f"{a_video_filename}_疾风罗莎.mp4"
                    output_path = os.path.join(self.output_dir, output_filename)

                    # 创建临时文件路径
                    temp_dir = os.path.join(self.output_dir, "temp")
                    os.makedirs(temp_dir, exist_ok=True)

                    temp_left = os.path.join(temp_dir, "temp_left.mp4")
                    left1_path = os.path.join(temp_dir, "left_1.mp4")
                    left2_path = os.path.join(temp_dir, "left_2.mp4")
                    temp_right = os.path.join(temp_dir, "temp_right.mp4")
                    right1_path = os.path.join(temp_dir, "right_1.mp4")
                    left3_path = os.path.join(temp_dir, "left_3.mp4")

                    # 选择B视频（如果有）
                    b_video_path = a_video_path  # 默认使用A视频
                    if self.b_video_list and len(self.b_video_list) > 0:
                        b_video_info = self.b_video_list[i % len(self.b_video_list)]
                        b_video_path = b_video_info['path'] if isinstance(b_video_info, dict) else b_video_info

                    # 复制A视频作为temp_left
                    import shutil
                    shutil.copy2(a_video_path, temp_left)

                    # 复制B视频作为temp_right
                    shutil.copy2(b_video_path, temp_right)

                    # 执行五步处理
                    self.progress_updated.emit(int((i * 100) / total_videos), f"处理中: {a_video_filename} - Step 1/5")
                    if not self.step1_process(temp_left, left1_path):
                        print(f"Step1处理失败: {a_video_filename}")
                        continue

                    self.progress_updated.emit(int((i * 100) / total_videos), f"处理中: {a_video_filename} - Step 2/5")
                    if not self.step2_process(left1_path, left2_path, width, height):
                        print(f"Step2处理失败: {a_video_filename}")
                        continue

                    self.progress_updated.emit(int((i * 100) / total_videos), f"处理中: {a_video_filename} - Step 3/5")
                    if not self.step3_process(temp_right, right1_path, width, height, duration):
                        print(f"Step3处理失败: {a_video_filename}")
                        continue

                    self.progress_updated.emit(int((i * 100) / total_videos), f"处理中: {a_video_filename} - Step 4/5")
                    if not self.step4_process(left2_path, right1_path, left3_path, duration):
                        print(f"Step4处理失败: {a_video_filename}")
                        continue

                    self.progress_updated.emit(int((i * 100) / total_videos), f"处理中: {a_video_filename} - Step 5/5 交错合成与最终输出")
                    if not self.step5_process(left1_path, left3_path, output_path, duration):
                        print(f"Step5处理失败: {a_video_filename}")
                        continue

                    # 清理临时文件
                    for temp_file in [temp_left, temp_right, left1_path, left2_path, right1_path, left3_path]:
                        if os.path.exists(temp_file):
                            try:
                                os.remove(temp_file)
                            except:
                                pass

                    success_count += 1
                    progress = int(((i + 1) * 100) / total_videos)
                    self.progress_updated.emit(progress, f"完成处理: {a_video_filename} ({i+1}/{total_videos})")

                except Exception as e:
                    print(f"处理视频失败 {a_video_info}: {e}")
                    continue

            # 清理临时目录
            temp_dir = os.path.join(self.output_dir, "temp")
            if os.path.exists(temp_dir):
                try:
                    os.rmdir(temp_dir)
                except:
                    pass

            if success_count > 0:
                self.process_finished.emit(True, f"成功处理 {success_count}/{total_videos} 个视频")
            else:
                self.process_finished.emit(False, "没有视频处理成功")

        except Exception as e:
            print(f"罗莎处理器运行异常: {e}")
            self.process_finished.emit(False, f"处理异常: {str(e)}")
